{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\modern\\\\QuizInterface.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbClock, TbChevronLeft, TbChevronRight, TbCheck, TbX, TbFlag, TbAlertCircle } from 'react-icons/tb';\nimport NewQuizRenderer from '../NewQuizRenderer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizInterface = ({\n  quiz,\n  questions = [],\n  onSubmit,\n  onExit,\n  className = ''\n}) => {\n  _s();\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeRemaining, setTimeRemaining] = useState(((quiz === null || quiz === void 0 ? void 0 : quiz.duration) || 30) * 60); // Convert to seconds\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const totalQuestions = questions.length;\n\n  // Timer effect\n  useEffect(() => {\n    if (timeRemaining <= 0) {\n      handleSubmit();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeRemaining(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeRemaining]);\n\n  // Handle answer selection\n  const handleAnswerChange = answer => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestionIndex]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < totalQuestions - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n  // Submit quiz\n  const handleSubmit = () => {\n    onSubmit && onSubmit(answers);\n  };\n  if (!questions || questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\uD83D\\uDCDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900 mb-2\",\n          children: \"No Questions Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"This quiz doesn't have any questions yet.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onExit,\n          className: \"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Back to Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(NewQuizRenderer, {\n    question: currentQuestion,\n    questionIndex: currentQuestionIndex,\n    totalQuestions: totalQuestions,\n    selectedAnswer: answers[currentQuestionIndex],\n    selectedOptions: answers,\n    onAnswerChange: handleAnswerChange,\n    timeLeft: timeRemaining,\n    examTitle: (quiz === null || quiz === void 0 ? void 0 : quiz.name) || \"Quiz\",\n    isTimeWarning: timeRemaining <= 60,\n    onNext: goToNext,\n    onPrevious: goToPrevious,\n    onSubmit: handleSubmit\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizInterface, \"7ocwzXENY7pktT2HMSg1vCvU88o=\");\n_c = QuizInterface;\nexport default QuizInterface;\nvar _c;\n$RefreshReg$(_c, \"QuizInterface\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "AnimatePresence", "TbClock", "TbChevronLeft", "TbChevronRight", "TbCheck", "TbX", "TbFlag", "TbAlertCircle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "QuizInterface", "quiz", "questions", "onSubmit", "onExit", "className", "_s", "currentQuestionIndex", "setCurrentQuestionIndex", "answers", "setAnswers", "timeRemaining", "setTimeRemaining", "duration", "currentQuestion", "totalQuestions", "length", "handleSubmit", "timer", "setInterval", "prev", "clearInterval", "handleAnswerChange", "answer", "goToNext", "goToPrevious", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "question", "questionIndex", "<PERSON><PERSON><PERSON><PERSON>", "selectedOptions", "onAnswerChange", "timeLeft", "examTitle", "name", "isTimeWarning", "onNext", "onPrevious", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/modern/QuizInterface.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Tb<PERSON>lock,\n  TbChevronLeft,\n  TbChevronRight,\n  TbCheck,\n  TbX,\n  TbFlag,\n  TbAlertCircle,\n} from 'react-icons/tb';\nimport NewQuiz<PERSON>enderer from '../NewQuizRenderer';\n\nconst QuizInterface = ({\n  quiz,\n  questions = [],\n  onSubmit,\n  onExit,\n  className = ''\n}) => {\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeRemaining, setTimeRemaining] = useState((quiz?.duration || 30) * 60); // Convert to seconds\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const totalQuestions = questions.length;\n\n  // Timer effect\n  useEffect(() => {\n    if (timeRemaining <= 0) {\n      handleSubmit();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeRemaining(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeRemaining]);\n\n  // Handle answer selection\n  const handleAnswerChange = (answer) => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestionIndex]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < totalQuestions - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n  // Submit quiz\n  const handleSubmit = () => {\n    onSubmit && onSubmit(answers);\n  };\n\n  if (!questions || questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-6xl mb-4\">📝</div>\n          <h2 className=\"text-xl font-bold text-gray-900 mb-2\">No Questions Available</h2>\n          <p className=\"text-gray-600 mb-4\">This quiz doesn't have any questions yet.</p>\n          <button\n            onClick={onExit}\n            className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Back to Dashboard\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <NewQuizRenderer\n      question={currentQuestion}\n      questionIndex={currentQuestionIndex}\n      totalQuestions={totalQuestions}\n      selectedAnswer={answers[currentQuestionIndex]}\n      selectedOptions={answers}\n      onAnswerChange={handleAnswerChange}\n      timeLeft={timeRemaining}\n      examTitle={quiz?.name || \"Quiz\"}\n      isTimeWarning={timeRemaining <= 60}\n      onNext={goToNext}\n      onPrevious={goToPrevious}\n      onSubmit={handleSubmit}\n    />\n  );\n};\nexport default QuizInterface;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,OAAO,EACPC,aAAa,EACbC,cAAc,EACdC,OAAO,EACPC,GAAG,EACHC,MAAM,EACNC,aAAa,QACR,gBAAgB;AACvB,OAAOC,eAAe,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,aAAa,GAAGA,CAAC;EACrBC,IAAI;EACJC,SAAS,GAAG,EAAE;EACdC,QAAQ;EACRC,MAAM;EACNC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAAgB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,QAAQ,KAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;EAEjF,MAAMC,eAAe,GAAGZ,SAAS,CAACK,oBAAoB,CAAC;EACvD,MAAMQ,cAAc,GAAGb,SAAS,CAACc,MAAM;;EAEvC;EACA9B,SAAS,CAAC,MAAM;IACd,IAAIyB,aAAa,IAAI,CAAC,EAAE;MACtBM,YAAY,CAAC,CAAC;MACd;IACF;IAEA,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BP,gBAAgB,CAACQ,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IACpC,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACP,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMW,kBAAkB,GAAIC,MAAM,IAAK;IACrCb,UAAU,CAACU,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACb,oBAAoB,GAAGgB;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIjB,oBAAoB,GAAGQ,cAAc,GAAG,CAAC,EAAE;MAC7CP,uBAAuB,CAACY,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAIlB,oBAAoB,GAAG,CAAC,EAAE;MAC5BC,uBAAuB,CAACY,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMH,YAAY,GAAGA,CAAA,KAAM;IACzBd,QAAQ,IAAIA,QAAQ,CAACM,OAAO,CAAC;EAC/B,CAAC;EAED,IAAI,CAACP,SAAS,IAAIA,SAAS,CAACc,MAAM,KAAK,CAAC,EAAE;IACxC,oBACEjB,OAAA;MAAKM,SAAS,EAAC,0DAA0D;MAAAqB,QAAA,eACvE3B,OAAA;QAAKM,SAAS,EAAC,aAAa;QAAAqB,QAAA,gBAC1B3B,OAAA;UAAKM,SAAS,EAAC,eAAe;UAAAqB,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACvC/B,OAAA;UAAIM,SAAS,EAAC,sCAAsC;UAAAqB,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChF/B,OAAA;UAAGM,SAAS,EAAC,oBAAoB;UAAAqB,QAAA,EAAC;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/E/B,OAAA;UACEgC,OAAO,EAAE3B,MAAO;UAChBC,SAAS,EAAC,iFAAiF;UAAAqB,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/B,OAAA,CAACF,eAAe;IACdmC,QAAQ,EAAElB,eAAgB;IAC1BmB,aAAa,EAAE1B,oBAAqB;IACpCQ,cAAc,EAAEA,cAAe;IAC/BmB,cAAc,EAAEzB,OAAO,CAACF,oBAAoB,CAAE;IAC9C4B,eAAe,EAAE1B,OAAQ;IACzB2B,cAAc,EAAEd,kBAAmB;IACnCe,QAAQ,EAAE1B,aAAc;IACxB2B,SAAS,EAAE,CAAArC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEsC,IAAI,KAAI,MAAO;IAChCC,aAAa,EAAE7B,aAAa,IAAI,EAAG;IACnC8B,MAAM,EAAEjB,QAAS;IACjBkB,UAAU,EAAEjB,YAAa;IACzBtB,QAAQ,EAAEc;EAAa;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxB,CAAC;AAEN,CAAC;AAACxB,EAAA,CAxFIN,aAAa;AAAA2C,EAAA,GAAb3C,aAAa;AAyFnB,eAAeA,aAAa;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}