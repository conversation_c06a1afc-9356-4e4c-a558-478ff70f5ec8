{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\NewQuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck, TbX, TbPhoto, TbEdit, TbCircleCheck, TbCircle } from 'react-icons/tb';\nimport './NewQuizRenderer.css';\n\n// Utility function to extract and normalize question data\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst extractQuestionData = question => {\n  if (!question) return null;\n  const questionText = question.name || question.question || question.text || question.title || '';\n  const questionType = question.type || question.answerType || question.questionType || 'mcq';\n  const questionOptions = question.options || question.choices || question.answers || {};\n  const correctAnswer = question.correctAnswer || question.correctOption || '';\n  const imageUrl = question.imageUrl || question.image || '';\n\n  // Normalize question type\n  let normalizedType = 'mcq';\n  const typeString = String(questionType).toLowerCase();\n  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {\n    normalizedType = 'mcq';\n  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {\n    normalizedType = 'fill';\n  } else if (typeString.includes('image') || typeString.includes('picture') || typeString.includes('diagram')) {\n    normalizedType = 'image';\n  }\n\n  // If no options are provided but it's marked as MCQ, treat as fill-in\n  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {\n    normalizedType = 'fill';\n  }\n  return {\n    text: questionText,\n    type: normalizedType,\n    options: questionOptions,\n    correctAnswer,\n    imageUrl,\n    originalQuestion: question\n  };\n};\n\n// Format time utility\nconst formatTime = seconds => {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n};\nconst NewQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false,\n  onSubmit\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // BYPASS extractQuestionData - it's causing issues, use direct question data\n  const questionData = null; // Force bypass to use fallback system\n\n  // Debug logging\n  useEffect(() => {\n    console.log('🎯 NewQuizRenderer Debug:', {\n      questionIndex,\n      totalQuestions,\n      hasQuestion: !!question,\n      hasQuestionData: !!questionData,\n      questionType: (question === null || question === void 0 ? void 0 : question.type) || (question === null || question === void 0 ? void 0 : question.answerType),\n      questionText: (question === null || question === void 0 ? void 0 : question.name) || (question === null || question === void 0 ? void 0 : question.question),\n      timeLeft\n    });\n  }, [question, questionData, questionIndex, totalQuestions, timeLeft]);\n\n  // Update current answer when selectedAnswer changes\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Handle answer selection\n  const handleAnswerChange = useCallback(answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(!!answer);\n    onAnswerChange(answer);\n  }, [onAnswerChange]);\n\n  // Handle navigation\n  const handleNext = useCallback(() => {\n    if (questionIndex === totalQuestions - 1) {\n      // Last question - submit quiz\n      if (onSubmit) {\n        onSubmit();\n      }\n    } else {\n      onNext();\n    }\n  }, [questionIndex, totalQuestions, onNext, onSubmit]);\n  const handlePrevious = useCallback(() => {\n    if (questionIndex > 0) {\n      onPrevious();\n    }\n  }, [questionIndex, onPrevious]);\n\n  // Always render - never show loading screen\n  // Create safe question data with fallbacks - be more aggressive about showing content\n  const safeQuestionData = {\n    text: (question === null || question === void 0 ? void 0 : question.name) || (question === null || question === void 0 ? void 0 : question.question) || (question === null || question === void 0 ? void 0 : question.text) || \"TEST QUESTION: What is 2 + 2?\",\n    type: (question === null || question === void 0 ? void 0 : question.type) || (question === null || question === void 0 ? void 0 : question.answerType) || 'mcq',\n    options: (question === null || question === void 0 ? void 0 : question.options) || (question === null || question === void 0 ? void 0 : question.choices) || {\n      A: (question === null || question === void 0 ? void 0 : question.optionA) || 'Option A: 3',\n      B: (question === null || question === void 0 ? void 0 : question.optionB) || 'Option B: 4',\n      C: (question === null || question === void 0 ? void 0 : question.optionC) || 'Option C: 5',\n      D: (question === null || question === void 0 ? void 0 : question.optionD) || 'Option D: 6'\n    },\n    imageUrl: (question === null || question === void 0 ? void 0 : question.imageUrl) || (question === null || question === void 0 ? void 0 : question.image) || (question === null || question === void 0 ? void 0 : question.diagram)\n  };\n\n  // Debug what we're actually rendering\n  console.log('🎯 NewQuizRenderer rendering:', {\n    hasQuestion: !!question,\n    hasQuestionData: !!questionData,\n    safeQuestionData,\n    questionIndex,\n    totalQuestions,\n    rawQuestion: question\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen flex flex-col bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '10px',\n        right: '10px',\n        background: 'red',\n        color: 'white',\n        padding: '10px',\n        zIndex: 9999\n      },\n      children: [\"QUIZ RENDERER ACTIVE - Q\", questionIndex + 1, \"/\", totalQuestions]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between gap-2 sm:gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-base sm:text-lg lg:text-xl font-bold text-gray-900 truncate\",\n              children: examTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs sm:text-sm text-gray-600 mt-1\",\n              children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-2 rounded-full font-bold text-xs sm:text-sm lg:text-base transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 animate-pulse' : 'bg-blue-100 text-blue-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 sm:mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full progress-bar\",\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${(questionIndex + 1) / totalQuestions * 100}%`\n              },\n              transition: {\n                duration: 0.5,\n                ease: \"easeOut\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-xs text-gray-500 mt-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [Math.round((questionIndex + 1) / totalQuestions * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'yellow',\n        padding: '20px',\n        margin: '20px',\n        fontSize: '24px',\n        fontWeight: 'bold'\n      },\n      children: [\"DEBUG: MAIN CONTENT AREA - Question \", questionIndex + 1, \" of \", totalQuestions, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), \"Question Text: \", safeQuestionData.text, /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this), \"Question Type: \", safeQuestionData.type]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto quiz-content\",\n      style: {\n        height: 'calc(100vh - 140px)',\n        paddingBottom: '100px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl tablet-layout desktop-layout mx-auto w-full px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8\",\n        children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          mode: \"wait\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            exit: {\n              opacity: 0,\n              x: -20\n            },\n            transition: {\n              duration: 0.3\n            },\n            className: \"bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-200 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-blue-600 to-indigo-700 px-4 sm:px-6 py-3 sm:py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `px-2 sm:px-3 py-1 rounded-full text-xs font-semibold ${safeQuestionData.type === 'mcq' ? 'bg-blue-100 text-blue-800' : safeQuestionData.type === 'fill' ? 'bg-green-100 text-green-800' : 'bg-purple-100 text-purple-800'}`,\n                  children: safeQuestionData.type === 'mcq' ? 'Multiple Choice' : safeQuestionData.type === 'fill' ? 'Fill in the Blank' : 'Image Question'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), isAnswered && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-1 text-white text-xs sm:text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                    className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"hidden sm:inline\",\n                    children: \"Answered\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 236,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 sm:p-6 lg:p-8 max-h-[calc(100vh-300px)] overflow-y-auto\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6 sm:mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"quiz-question-text text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 mb-4 leading-relaxed\",\n                  children: safeQuestionData.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this), safeQuestionData.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: safeQuestionData.imageUrl,\n                      alt: \"Question diagram\",\n                      className: \"quiz-image w-full max-w-full sm:max-w-lg lg:max-w-2xl mx-auto h-auto object-contain max-h-64 sm:max-h-80 lg:max-h-96\",\n                      style: {\n                        display: 'block'\n                      },\n                      onLoad: e => {\n                        e.target.style.display = 'block';\n                        const fallback = e.target.parentNode.querySelector('.image-fallback');\n                        if (fallback) fallback.style.display = 'none';\n                      },\n                      onError: e => {\n                        e.target.style.display = 'none';\n                        const fallback = e.target.parentNode.querySelector('.image-fallback');\n                        if (fallback) fallback.style.display = 'flex';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 254,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"image-fallback hidden items-center justify-center h-32 sm:h-48 text-gray-500\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center\",\n                        children: [/*#__PURE__*/_jsxDEV(TbPhoto, {\n                          className: \"w-8 h-8 sm:w-12 sm:h-12 mx-auto mb-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 272,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm sm:text-base\",\n                          children: \"Image not available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 273,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 271,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 270,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 253,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3 sm:space-y-4\",\n                children: safeQuestionData.type === 'mcq' && safeQuestionData.options && Object.keys(safeQuestionData.options).length > 0 ?\n                // Multiple Choice Questions\n                Object.entries(safeQuestionData.options).map(([key, value]) => {\n                  const isSelected = currentAnswer === key;\n                  return /*#__PURE__*/_jsxDEV(motion.button, {\n                    onClick: () => handleAnswerChange(key),\n                    className: `quiz-option-button quiz-button w-full p-3 sm:p-4 rounded-lg sm:rounded-xl border-2 text-left transition-all duration-200 ${isSelected ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'}`,\n                    whileHover: {\n                      scale: 1.01\n                    },\n                    whileTap: {\n                      scale: 0.99\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-start gap-3 sm:gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `w-7 h-7 sm:w-8 sm:h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm flex-shrink-0 mt-0.5 ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-600'}`,\n                        children: isSelected ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 305,\n                          columnNumber: 45\n                        }, this) : key\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 300,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"quiz-option-text text-gray-900 font-medium text-sm sm:text-base leading-relaxed break-words\",\n                        children: value\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 307,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 299,\n                      columnNumber: 27\n                    }, this)\n                  }, key, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 288,\n                    columnNumber: 25\n                  }, this);\n                }) :\n                /*#__PURE__*/\n                // Fill-in-the-Blank Questions\n                _jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg sm:rounded-xl p-4 sm:p-6 border-2 border-green-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-7 h-7 sm:w-8 sm:h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(TbEdit, {\n                          className: \"w-4 h-4 sm:w-5 sm:h-5 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 318,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 317,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"text-sm font-semibold text-gray-700\",\n                        children: \"Your Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      value: currentAnswer,\n                      onChange: e => handleAnswerChange(e.target.value),\n                      placeholder: \"Type your answer here...\",\n                      className: \"w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base\",\n                      rows: \"4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 324,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this)]\n          }, questionIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-footer bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl tablet-layout desktop-layout mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4 mobile-nav\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between gap-2 sm:gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handlePrevious,\n            disabled: questionIndex === 0,\n            className: `quiz-nav-button quiz-button mobile-nav-button flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sm:hidden\",\n              children: \"Prev\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-[200px] sm:max-w-xs lg:max-w-md scrollbar-hide\",\n            children: Array.from({\n              length: totalQuestions\n            }, (_, index) => {\n              const isAnswered = selectedOptions[index] !== undefined && selectedOptions[index] !== '';\n              const isCurrent = index === questionIndex;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `quiz-nav-button w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${isCurrent ? 'bg-blue-600 text-white scale-110' : isAnswered ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600'}`,\n                children: isAnswered && !isCurrent ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 23\n                }, this) : index + 1\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleNext,\n            className: \"quiz-nav-button quiz-button mobile-nav-button flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg sm:rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sm:hidden\",\n              children: questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 399,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this);\n};\n_s(NewQuizRenderer, \"/AKwHDMDUIl41hZRNod4g0BrOFc=\");\n_c = NewQuizRenderer;\nexport default NewQuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"NewQuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "AnimatePresence", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbX", "TbPhoto", "TbEdit", "TbCircleCheck", "TbCircle", "jsxDEV", "_jsxDEV", "extractQuestionData", "question", "questionText", "name", "text", "title", "questionType", "type", "answerType", "questionOptions", "options", "choices", "answers", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "imageUrl", "image", "normalizedType", "typeString", "String", "toLowerCase", "includes", "Object", "keys", "length", "originalQuestion", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "selectedOptions", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "onSubmit", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "questionData", "console", "log", "hasQuestion", "hasQuestionData", "handleAnswerChange", "answer", "handleNext", "handlePrevious", "safeQuestionData", "A", "optionA", "B", "optionB", "C", "optionC", "D", "optionD", "diagram", "rawQuestion", "className", "children", "style", "position", "top", "right", "background", "color", "padding", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "width", "animate", "transition", "duration", "ease", "round", "margin", "fontSize", "fontWeight", "height", "paddingBottom", "mode", "opacity", "x", "exit", "src", "alt", "display", "onLoad", "e", "target", "fallback", "parentNode", "querySelector", "onError", "entries", "map", "key", "value", "isSelected", "button", "onClick", "whileHover", "scale", "whileTap", "onChange", "placeholder", "rows", "disabled", "Array", "from", "_", "index", "undefined", "isCurrent", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/NewQuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Tb<PERSON><PERSON>,\n  TbArrowLeft,\n  TbArrowRight,\n  TbCheck,\n  TbX,\n  TbPhoto,\n  TbEdit,\n  TbCircleCheck,\n  TbCircle\n} from 'react-icons/tb';\nimport './NewQuizRenderer.css';\n\n// Utility function to extract and normalize question data\nconst extractQuestionData = (question) => {\n  if (!question) return null;\n\n  const questionText = question.name || question.question || question.text || question.title || '';\n  const questionType = question.type || question.answerType || question.questionType || 'mcq';\n  const questionOptions = question.options || question.choices || question.answers || {};\n  const correctAnswer = question.correctAnswer || question.correctOption || '';\n  const imageUrl = question.imageUrl || question.image || '';\n\n  // Normalize question type\n  let normalizedType = 'mcq';\n  const typeString = String(questionType).toLowerCase();\n\n  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {\n    normalizedType = 'mcq';\n  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {\n    normalizedType = 'fill';\n  } else if (typeString.includes('image') || typeString.includes('picture') || typeString.includes('diagram')) {\n    normalizedType = 'image';\n  }\n\n  // If no options are provided but it's marked as MCQ, treat as fill-in\n  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {\n    normalizedType = 'fill';\n  }\n\n  return {\n    text: questionText,\n    type: normalizedType,\n    options: questionOptions,\n    correctAnswer,\n    imageUrl,\n    originalQuestion: question\n  };\n};\n\n// Format time utility\nconst formatTime = (seconds) => {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n};\n\nconst NewQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false,\n  onSubmit\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // BYPASS extractQuestionData - it's causing issues, use direct question data\n  const questionData = null; // Force bypass to use fallback system\n\n  // Debug logging\n  useEffect(() => {\n    console.log('🎯 NewQuizRenderer Debug:', {\n      questionIndex,\n      totalQuestions,\n      hasQuestion: !!question,\n      hasQuestionData: !!questionData,\n      questionType: question?.type || question?.answerType,\n      questionText: question?.name || question?.question,\n      timeLeft\n    });\n  }, [question, questionData, questionIndex, totalQuestions, timeLeft]);\n\n  // Update current answer when selectedAnswer changes\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Handle answer selection\n  const handleAnswerChange = useCallback((answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(!!answer);\n    onAnswerChange(answer);\n  }, [onAnswerChange]);\n\n  // Handle navigation\n  const handleNext = useCallback(() => {\n    if (questionIndex === totalQuestions - 1) {\n      // Last question - submit quiz\n      if (onSubmit) {\n        onSubmit();\n      }\n    } else {\n      onNext();\n    }\n  }, [questionIndex, totalQuestions, onNext, onSubmit]);\n\n  const handlePrevious = useCallback(() => {\n    if (questionIndex > 0) {\n      onPrevious();\n    }\n  }, [questionIndex, onPrevious]);\n\n  // Always render - never show loading screen\n  // Create safe question data with fallbacks - be more aggressive about showing content\n  const safeQuestionData = {\n    text: question?.name || question?.question || question?.text || \"TEST QUESTION: What is 2 + 2?\",\n    type: question?.type || question?.answerType || 'mcq',\n    options: question?.options || question?.choices || {\n      A: question?.optionA || 'Option A: 3',\n      B: question?.optionB || 'Option B: 4',\n      C: question?.optionC || 'Option C: 5',\n      D: question?.optionD || 'Option D: 6'\n    },\n    imageUrl: question?.imageUrl || question?.image || question?.diagram\n  };\n\n  // Debug what we're actually rendering\n  console.log('🎯 NewQuizRenderer rendering:', {\n    hasQuestion: !!question,\n    hasQuestionData: !!questionData,\n    safeQuestionData,\n    questionIndex,\n    totalQuestions,\n    rawQuestion: question\n  });\n\n  return (\n    <div className=\"h-screen flex flex-col bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container\">\n      {/* DEBUG: Test if component renders */}\n      <div style={{position: 'fixed', top: '10px', right: '10px', background: 'red', color: 'white', padding: '10px', zIndex: 9999}}>\n        QUIZ RENDERER ACTIVE - Q{questionIndex + 1}/{totalQuestions}\n      </div>\n\n      {/* Header with Timer and Quiz Info */}\n      <div className=\"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50 flex-shrink-0\">\n        <div className=\"max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\">\n          <div className=\"flex items-center justify-between gap-2 sm:gap-4\">\n            {/* Quiz Title and Question Counter */}\n            <div className=\"flex-1 min-w-0\">\n              <h1 className=\"text-base sm:text-lg lg:text-xl font-bold text-gray-900 truncate\">\n                {examTitle}\n              </h1>\n              <p className=\"text-xs sm:text-sm text-gray-600 mt-1\">\n                Question {questionIndex + 1} of {totalQuestions}\n              </p>\n            </div>\n\n            {/* Timer */}\n            <div className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-2 rounded-full font-bold text-xs sm:text-sm lg:text-base transition-all ${\n              isTimeWarning\n                ? 'bg-red-100 text-red-700 animate-pulse'\n                : 'bg-blue-100 text-blue-700'\n            }`}>\n              <TbClock className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              <span>{formatTime(timeLeft)}</span>\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mt-3 sm:mt-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full progress-bar\"\n                initial={{ width: 0 }}\n                animate={{ width: `${((questionIndex + 1) / totalQuestions) * 100}%` }}\n                transition={{ duration: 0.5, ease: \"easeOut\" }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n              <span>Progress</span>\n              <span>{Math.round(((questionIndex + 1) / totalQuestions) * 100)}%</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* DEBUG: Test content visibility */}\n      <div style={{background: 'yellow', padding: '20px', margin: '20px', fontSize: '24px', fontWeight: 'bold'}}>\n        DEBUG: MAIN CONTENT AREA - Question {questionIndex + 1} of {totalQuestions}\n        <br />\n        Question Text: {safeQuestionData.text}\n        <br />\n        Question Type: {safeQuestionData.type}\n      </div>\n\n      {/* Main Content - Scrollable with proper height */}\n      <div className=\"flex-1 overflow-y-auto quiz-content\" style={{ height: 'calc(100vh - 140px)', paddingBottom: '100px' }}>\n        <div className=\"max-w-4xl tablet-layout desktop-layout mx-auto w-full px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8\">\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={questionIndex}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              transition={{ duration: 0.3 }}\n              className=\"bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-200 overflow-hidden\"\n            >\n              {/* Question Header */}\n              <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 px-4 sm:px-6 py-3 sm:py-4\">\n                <div className=\"flex items-center justify-between gap-2\">\n                  <div className={`px-2 sm:px-3 py-1 rounded-full text-xs font-semibold ${\n                    safeQuestionData.type === 'mcq'\n                      ? 'bg-blue-100 text-blue-800'\n                      : safeQuestionData.type === 'fill'\n                      ? 'bg-green-100 text-green-800'\n                      : 'bg-purple-100 text-purple-800'\n                  }`}>\n                    {safeQuestionData.type === 'mcq' ? 'Multiple Choice' :\n                     safeQuestionData.type === 'fill' ? 'Fill in the Blank' :\n                     'Image Question'}\n                  </div>\n                  {isAnswered && (\n                    <div className=\"flex items-center gap-1 text-white text-xs sm:text-sm font-medium\">\n                      <TbCheck className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n                      <span className=\"hidden sm:inline\">Answered</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              {/* Question Content - Scrollable */}\n              <div className=\"p-4 sm:p-6 lg:p-8 max-h-[calc(100vh-300px)] overflow-y-auto\">\n                {/* Question Text */}\n                <div className=\"mb-6 sm:mb-8\">\n                  <h2 className=\"quiz-question-text text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 mb-4 leading-relaxed\">\n                    {safeQuestionData.text}\n                  </h2>\n\n                  {/* Image for image-based questions */}\n                  {safeQuestionData.imageUrl && (\n                    <div className=\"mb-6\">\n                      <div className=\"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200\">\n                        <img\n                          src={safeQuestionData.imageUrl}\n                          alt=\"Question diagram\"\n                          className=\"quiz-image w-full max-w-full sm:max-w-lg lg:max-w-2xl mx-auto h-auto object-contain max-h-64 sm:max-h-80 lg:max-h-96\"\n                          style={{ display: 'block' }}\n                          onLoad={(e) => {\n                            e.target.style.display = 'block';\n                            const fallback = e.target.parentNode.querySelector('.image-fallback');\n                            if (fallback) fallback.style.display = 'none';\n                          }}\n                          onError={(e) => {\n                            e.target.style.display = 'none';\n                            const fallback = e.target.parentNode.querySelector('.image-fallback');\n                            if (fallback) fallback.style.display = 'flex';\n                          }}\n                        />\n                        <div className=\"image-fallback hidden items-center justify-center h-32 sm:h-48 text-gray-500\">\n                          <div className=\"text-center\">\n                            <TbPhoto className=\"w-8 h-8 sm:w-12 sm:h-12 mx-auto mb-2\" />\n                            <p className=\"text-sm sm:text-base\">Image not available</p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                {/* Answer Options */}\n                <div className=\"space-y-3 sm:space-y-4\">\n                  {safeQuestionData.type === 'mcq' && safeQuestionData.options && Object.keys(safeQuestionData.options).length > 0 ? (\n                    // Multiple Choice Questions\n                    Object.entries(safeQuestionData.options).map(([key, value]) => {\n                      const isSelected = currentAnswer === key;\n                      return (\n                        <motion.button\n                          key={key}\n                          onClick={() => handleAnswerChange(key)}\n                          className={`quiz-option-button quiz-button w-full p-3 sm:p-4 rounded-lg sm:rounded-xl border-2 text-left transition-all duration-200 ${\n                            isSelected\n                              ? 'border-blue-500 bg-blue-50 shadow-md'\n                              : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'\n                          }`}\n                          whileHover={{ scale: 1.01 }}\n                          whileTap={{ scale: 0.99 }}\n                        >\n                          <div className=\"flex items-start gap-3 sm:gap-4\">\n                            <div className={`w-7 h-7 sm:w-8 sm:h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm flex-shrink-0 mt-0.5 ${\n                              isSelected\n                                ? 'border-blue-500 bg-blue-500 text-white'\n                                : 'border-gray-300 text-gray-600'\n                            }`}>\n                              {isSelected ? <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5\" /> : key}\n                            </div>\n                            <span className=\"quiz-option-text text-gray-900 font-medium text-sm sm:text-base leading-relaxed break-words\">{value}</span>\n                          </div>\n                        </motion.button>\n                      );\n                    })\n                  ) : (\n                    // Fill-in-the-Blank Questions\n                    <div className=\"space-y-4\">\n                      <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg sm:rounded-xl p-4 sm:p-6 border-2 border-green-200\">\n                        <div className=\"flex items-center gap-2 mb-4\">\n                          <div className=\"w-7 h-7 sm:w-8 sm:h-8 bg-green-500 rounded-full flex items-center justify-center\">\n                            <TbEdit className=\"w-4 h-4 sm:w-5 sm:h-5 text-white\" />\n                          </div>\n                          <label className=\"text-sm font-semibold text-gray-700\">\n                            Your Answer:\n                          </label>\n                        </div>\n                        <textarea\n                          value={currentAnswer}\n                          onChange={(e) => handleAnswerChange(e.target.value)}\n                          placeholder=\"Type your answer here...\"\n                          className=\"w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base\"\n                          rows=\"4\"\n                        />\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </motion.div>\n          </AnimatePresence>\n        </div>\n      </div>\n\n      {/* Navigation Footer - Fixed at bottom */}\n      <div className=\"quiz-footer bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50 flex-shrink-0\">\n        <div className=\"max-w-4xl tablet-layout desktop-layout mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4 mobile-nav\">\n          <div className=\"flex items-center justify-between gap-2 sm:gap-4\">\n            {/* Previous Button */}\n            <button\n              onClick={handlePrevious}\n              disabled={questionIndex === 0}\n              className={`quiz-nav-button quiz-button mobile-nav-button flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base ${\n                questionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'\n              }`}\n            >\n              <TbArrowLeft className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              <span className=\"hidden sm:inline\">Previous</span>\n              <span className=\"sm:hidden\">Prev</span>\n            </button>\n\n            {/* Question Navigation Dots */}\n            <div className=\"flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-[200px] sm:max-w-xs lg:max-w-md scrollbar-hide\">\n              {Array.from({ length: totalQuestions }, (_, index) => {\n                const isAnswered = selectedOptions[index] !== undefined && selectedOptions[index] !== '';\n                const isCurrent = index === questionIndex;\n\n                return (\n                  <div\n                    key={index}\n                    className={`quiz-nav-button w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${\n                      isCurrent\n                        ? 'bg-blue-600 text-white scale-110'\n                        : isAnswered\n                        ? 'bg-green-500 text-white'\n                        : 'bg-gray-200 text-gray-600'\n                    }`}\n                  >\n                    {isAnswered && !isCurrent ? (\n                      <TbCheck className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n                    ) : (\n                      index + 1\n                    )}\n                  </div>\n                );\n              })}\n            </div>\n\n            {/* Next/Submit Button */}\n            <button\n              onClick={handleNext}\n              className=\"quiz-nav-button quiz-button mobile-nav-button flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg sm:rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base\"\n            >\n              <span className=\"hidden sm:inline\">\n                {questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'}\n              </span>\n              <span className=\"sm:hidden\">\n                {questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'}\n              </span>\n              {questionIndex === totalQuestions - 1 ? (\n                <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              ) : (\n                <TbArrowRight className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NewQuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,aAAa,EACbC,QAAQ,QACH,gBAAgB;AACvB,OAAO,uBAAuB;;AAE9B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAIC,QAAQ,IAAK;EACxC,IAAI,CAACA,QAAQ,EAAE,OAAO,IAAI;EAE1B,MAAMC,YAAY,GAAGD,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACA,QAAQ,IAAIA,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACI,KAAK,IAAI,EAAE;EAChG,MAAMC,YAAY,GAAGL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACO,UAAU,IAAIP,QAAQ,CAACK,YAAY,IAAI,KAAK;EAC3F,MAAMG,eAAe,GAAGR,QAAQ,CAACS,OAAO,IAAIT,QAAQ,CAACU,OAAO,IAAIV,QAAQ,CAACW,OAAO,IAAI,CAAC,CAAC;EACtF,MAAMC,aAAa,GAAGZ,QAAQ,CAACY,aAAa,IAAIZ,QAAQ,CAACa,aAAa,IAAI,EAAE;EAC5E,MAAMC,QAAQ,GAAGd,QAAQ,CAACc,QAAQ,IAAId,QAAQ,CAACe,KAAK,IAAI,EAAE;;EAE1D;EACA,IAAIC,cAAc,GAAG,KAAK;EAC1B,MAAMC,UAAU,GAAGC,MAAM,CAACb,YAAY,CAAC,CAACc,WAAW,CAAC,CAAC;EAErD,IAAIF,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,IAAIH,UAAU,KAAK,KAAK,IAAIA,UAAU,CAACG,QAAQ,CAAC,UAAU,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,EAAE;IAC7HJ,cAAc,GAAG,KAAK;EACxB,CAAC,MAAM,IAAIC,UAAU,CAACG,QAAQ,CAAC,MAAM,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,UAAU,KAAK,MAAM,IAAIA,UAAU,CAACG,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC9HJ,cAAc,GAAG,MAAM;EACzB,CAAC,MAAM,IAAIC,UAAU,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,SAAS,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,SAAS,CAAC,EAAE;IAC3GJ,cAAc,GAAG,OAAO;EAC1B;;EAEA;EACA,IAAIA,cAAc,KAAK,KAAK,KAAK,CAACR,eAAe,IAAIa,MAAM,CAACC,IAAI,CAACd,eAAe,CAAC,CAACe,MAAM,KAAK,CAAC,CAAC,EAAE;IAC/FP,cAAc,GAAG,MAAM;EACzB;EAEA,OAAO;IACLb,IAAI,EAAEF,YAAY;IAClBK,IAAI,EAAEU,cAAc;IACpBP,OAAO,EAAED,eAAe;IACxBI,aAAa;IACbE,QAAQ;IACRU,gBAAgB,EAAExB;EACpB,CAAC;AACH,CAAC;;AAED;AACA,MAAMyB,UAAU,GAAIC,OAAO,IAAK;EAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;EACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;EACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;AACrE,CAAC;AAED,MAAMC,eAAe,GAAGA,CAAC;EACvBjC,QAAQ;EACRkC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,eAAe,GAAG,CAAC,CAAC;EACpBC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG,KAAK;EACrBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhE,QAAQ,CAACqD,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAMmE,YAAY,GAAG,IAAI,CAAC,CAAC;;EAE3B;EACAlE,SAAS,CAAC,MAAM;IACdmE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvClB,aAAa;MACbC,cAAc;MACdkB,WAAW,EAAE,CAAC,CAACrD,QAAQ;MACvBsD,eAAe,EAAE,CAAC,CAACJ,YAAY;MAC/B7C,YAAY,EAAE,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,IAAI,MAAIN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,UAAU;MACpDN,YAAY,EAAE,CAAAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,IAAI,MAAIF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEA,QAAQ;MAClDuC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvC,QAAQ,EAAEkD,YAAY,EAAEhB,aAAa,EAAEC,cAAc,EAAEI,QAAQ,CAAC,CAAC;;EAErE;EACAvD,SAAS,CAAC,MAAM;IACd+D,gBAAgB,CAACX,cAAc,IAAI,EAAE,CAAC;IACtCa,aAAa,CAAC,CAAC,CAACb,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;;EAEnC;EACA,MAAMqB,kBAAkB,GAAGtE,WAAW,CAAEuE,MAAM,IAAK;IACjDT,gBAAgB,CAACS,MAAM,CAAC;IACxBP,aAAa,CAAC,CAAC,CAACO,MAAM,CAAC;IACvBlB,cAAc,CAACkB,MAAM,CAAC;EACxB,CAAC,EAAE,CAAClB,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMmB,UAAU,GAAGxE,WAAW,CAAC,MAAM;IACnC,IAAIiD,aAAa,KAAKC,cAAc,GAAG,CAAC,EAAE;MACxC;MACA,IAAIS,QAAQ,EAAE;QACZA,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,MAAM;MACLJ,MAAM,CAAC,CAAC;IACV;EACF,CAAC,EAAE,CAACN,aAAa,EAAEC,cAAc,EAAEK,MAAM,EAAEI,QAAQ,CAAC,CAAC;EAErD,MAAMc,cAAc,GAAGzE,WAAW,CAAC,MAAM;IACvC,IAAIiD,aAAa,GAAG,CAAC,EAAE;MACrBO,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACP,aAAa,EAAEO,UAAU,CAAC,CAAC;;EAE/B;EACA;EACA,MAAMkB,gBAAgB,GAAG;IACvBxD,IAAI,EAAE,CAAAH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,IAAI,MAAIF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEA,QAAQ,MAAIA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,IAAI,KAAI,+BAA+B;IAC/FG,IAAI,EAAE,CAAAN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,IAAI,MAAIN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,UAAU,KAAI,KAAK;IACrDE,OAAO,EAAE,CAAAT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,OAAO,MAAIT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEU,OAAO,KAAI;MACjDkD,CAAC,EAAE,CAAA5D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6D,OAAO,KAAI,aAAa;MACrCC,CAAC,EAAE,CAAA9D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE+D,OAAO,KAAI,aAAa;MACrCC,CAAC,EAAE,CAAAhE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiE,OAAO,KAAI,aAAa;MACrCC,CAAC,EAAE,CAAAlE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmE,OAAO,KAAI;IAC1B,CAAC;IACDrD,QAAQ,EAAE,CAAAd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEc,QAAQ,MAAId,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEe,KAAK,MAAIf,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEoE,OAAO;EACtE,CAAC;;EAED;EACAjB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;IAC3CC,WAAW,EAAE,CAAC,CAACrD,QAAQ;IACvBsD,eAAe,EAAE,CAAC,CAACJ,YAAY;IAC/BS,gBAAgB;IAChBzB,aAAa;IACbC,cAAc;IACdkC,WAAW,EAAErE;EACf,CAAC,CAAC;EAEF,oBACEF,OAAA;IAAKwE,SAAS,EAAC,oFAAoF;IAAAC,QAAA,gBAEjGzE,OAAA;MAAK0E,KAAK,EAAE;QAACC,QAAQ,EAAE,OAAO;QAAEC,GAAG,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,UAAU,EAAE,KAAK;QAAEC,KAAK,EAAE,OAAO;QAAEC,OAAO,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAI,CAAE;MAAAR,QAAA,GAAC,0BACrG,EAACrC,aAAa,GAAG,CAAC,EAAC,GAAC,EAACC,cAAc;IAAA;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC,eAGNrF,OAAA;MAAKwE,SAAS,EAAC,+EAA+E;MAAAC,QAAA,eAC5FzE,OAAA;QAAKwE,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClEzE,OAAA;UAAKwE,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAE/DzE,OAAA;YAAKwE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzE,OAAA;cAAIwE,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7E7B;YAAS;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACLrF,OAAA;cAAGwE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,GAAC,WAC1C,EAACrC,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNrF,OAAA;YAAKwE,SAAS,EAAG,4HACf3B,aAAa,GACT,uCAAuC,GACvC,2BACL,EAAE;YAAA4B,QAAA,gBACDzE,OAAA,CAACV,OAAO;cAACkF,SAAS,EAAC;YAAuB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CrF,OAAA;cAAAyE,QAAA,EAAO9C,UAAU,CAACc,QAAQ;YAAC;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrF,OAAA;UAAKwE,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzE,OAAA;YAAKwE,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDzE,OAAA,CAACZ,MAAM,CAACkG,GAAG;cACTd,SAAS,EAAC,4EAA4E;cACtFe,OAAO,EAAE;gBAAEC,KAAK,EAAE;cAAE,CAAE;cACtBC,OAAO,EAAE;gBAAED,KAAK,EAAG,GAAG,CAACpD,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAI;cAAG,CAAE;cACvEqD,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,IAAI,EAAE;cAAU;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNrF,OAAA;YAAKwE,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DzE,OAAA;cAAAyE,QAAA,EAAM;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrBrF,OAAA;cAAAyE,QAAA,GAAO3C,IAAI,CAAC+D,KAAK,CAAE,CAACzD,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG,CAAC,EAAC,GAAC;YAAA;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrF,OAAA;MAAK0E,KAAK,EAAE;QAACI,UAAU,EAAE,QAAQ;QAAEE,OAAO,EAAE,MAAM;QAAEc,MAAM,EAAE,MAAM;QAAEC,QAAQ,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAM,CAAE;MAAAvB,QAAA,GAAC,sCACrE,EAACrC,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc,eAC1ErC,OAAA;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,mBACS,EAACxB,gBAAgB,CAACxD,IAAI,eACrCL,OAAA;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,mBACS,EAACxB,gBAAgB,CAACrD,IAAI;IAAA;MAAA0E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC,eAGNrF,OAAA;MAAKwE,SAAS,EAAC,qCAAqC;MAACE,KAAK,EAAE;QAAEuB,MAAM,EAAE,qBAAqB;QAAEC,aAAa,EAAE;MAAQ,CAAE;MAAAzB,QAAA,eACpHzE,OAAA;QAAKwE,SAAS,EAAC,iGAAiG;QAAAC,QAAA,eAC9GzE,OAAA,CAACX,eAAe;UAAC8G,IAAI,EAAC,MAAM;UAAA1B,QAAA,eAC1BzE,OAAA,CAACZ,MAAM,CAACkG,GAAG;YAETC,OAAO,EAAE;cAAEa,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BZ,OAAO,EAAE;cAAEW,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BC,IAAI,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAC7BX,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BnB,SAAS,EAAC,qFAAqF;YAAAC,QAAA,gBAG/FzE,OAAA;cAAKwE,SAAS,EAAC,wEAAwE;cAAAC,QAAA,eACrFzE,OAAA;gBAAKwE,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACtDzE,OAAA;kBAAKwE,SAAS,EAAG,wDACfX,gBAAgB,CAACrD,IAAI,KAAK,KAAK,GAC3B,2BAA2B,GAC3BqD,gBAAgB,CAACrD,IAAI,KAAK,MAAM,GAChC,6BAA6B,GAC7B,+BACL,EAAE;kBAAAiE,QAAA,EACAZ,gBAAgB,CAACrD,IAAI,KAAK,KAAK,GAAG,iBAAiB,GACnDqD,gBAAgB,CAACrD,IAAI,KAAK,MAAM,GAAG,mBAAmB,GACtD;gBAAgB;kBAAA0E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,EACLnC,UAAU,iBACTlD,OAAA;kBAAKwE,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,gBAChFzE,OAAA,CAACP,OAAO;oBAAC+E,SAAS,EAAC;kBAAuB;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7CrF,OAAA;oBAAMwE,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNrF,OAAA;cAAKwE,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBAE1EzE,OAAA;gBAAKwE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BzE,OAAA;kBAAIwE,SAAS,EAAC,oGAAoG;kBAAAC,QAAA,EAC/GZ,gBAAgB,CAACxD;gBAAI;kBAAA6E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,EAGJxB,gBAAgB,CAAC7C,QAAQ,iBACxBhB,OAAA;kBAAKwE,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnBzE,OAAA;oBAAKwE,SAAS,EAAC,uEAAuE;oBAAAC,QAAA,gBACpFzE,OAAA;sBACEuG,GAAG,EAAE1C,gBAAgB,CAAC7C,QAAS;sBAC/BwF,GAAG,EAAC,kBAAkB;sBACtBhC,SAAS,EAAC,sHAAsH;sBAChIE,KAAK,EAAE;wBAAE+B,OAAO,EAAE;sBAAQ,CAAE;sBAC5BC,MAAM,EAAGC,CAAC,IAAK;wBACbA,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAC+B,OAAO,GAAG,OAAO;wBAChC,MAAMI,QAAQ,GAAGF,CAAC,CAACC,MAAM,CAACE,UAAU,CAACC,aAAa,CAAC,iBAAiB,CAAC;wBACrE,IAAIF,QAAQ,EAAEA,QAAQ,CAACnC,KAAK,CAAC+B,OAAO,GAAG,MAAM;sBAC/C,CAAE;sBACFO,OAAO,EAAGL,CAAC,IAAK;wBACdA,CAAC,CAACC,MAAM,CAAClC,KAAK,CAAC+B,OAAO,GAAG,MAAM;wBAC/B,MAAMI,QAAQ,GAAGF,CAAC,CAACC,MAAM,CAACE,UAAU,CAACC,aAAa,CAAC,iBAAiB,CAAC;wBACrE,IAAIF,QAAQ,EAAEA,QAAQ,CAACnC,KAAK,CAAC+B,OAAO,GAAG,MAAM;sBAC/C;oBAAE;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFrF,OAAA;sBAAKwE,SAAS,EAAC,8EAA8E;sBAAAC,QAAA,eAC3FzE,OAAA;wBAAKwE,SAAS,EAAC,aAAa;wBAAAC,QAAA,gBAC1BzE,OAAA,CAACL,OAAO;0BAAC6E,SAAS,EAAC;wBAAsC;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC5DrF,OAAA;0BAAGwE,SAAS,EAAC,sBAAsB;0BAAAC,QAAA,EAAC;wBAAmB;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNrF,OAAA;gBAAKwE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACpCZ,gBAAgB,CAACrD,IAAI,KAAK,KAAK,IAAIqD,gBAAgB,CAAClD,OAAO,IAAIY,MAAM,CAACC,IAAI,CAACqC,gBAAgB,CAAClD,OAAO,CAAC,CAACc,MAAM,GAAG,CAAC;gBAC9G;gBACAF,MAAM,CAAC0F,OAAO,CAACpD,gBAAgB,CAAClD,OAAO,CAAC,CAACuG,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;kBAC7D,MAAMC,UAAU,GAAGrE,aAAa,KAAKmE,GAAG;kBACxC,oBACEnH,OAAA,CAACZ,MAAM,CAACkI,MAAM;oBAEZC,OAAO,EAAEA,CAAA,KAAM9D,kBAAkB,CAAC0D,GAAG,CAAE;oBACvC3C,SAAS,EAAG,4HACV6C,UAAU,GACN,sCAAsC,GACtC,iEACL,EAAE;oBACHG,UAAU,EAAE;sBAAEC,KAAK,EAAE;oBAAK,CAAE;oBAC5BC,QAAQ,EAAE;sBAAED,KAAK,EAAE;oBAAK,CAAE;oBAAAhD,QAAA,eAE1BzE,OAAA;sBAAKwE,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,gBAC9CzE,OAAA;wBAAKwE,SAAS,EAAG,uHACf6C,UAAU,GACN,wCAAwC,GACxC,+BACL,EAAE;wBAAA5C,QAAA,EACA4C,UAAU,gBAAGrH,OAAA,CAACP,OAAO;0BAAC+E,SAAS,EAAC;wBAAuB;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,GAAG8B;sBAAG;wBAAAjC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9D,CAAC,eACNrF,OAAA;wBAAMwE,SAAS,EAAC,6FAA6F;wBAAAC,QAAA,EAAE2C;sBAAK;wBAAAlC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzH;kBAAC,GAnBD8B,GAAG;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAoBK,CAAC;gBAEpB,CAAC,CAAC;gBAAA;gBAEF;gBACArF,OAAA;kBAAKwE,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxBzE,OAAA;oBAAKwE,SAAS,EAAC,4GAA4G;oBAAAC,QAAA,gBACzHzE,OAAA;sBAAKwE,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3CzE,OAAA;wBAAKwE,SAAS,EAAC,kFAAkF;wBAAAC,QAAA,eAC/FzE,OAAA,CAACJ,MAAM;0BAAC4E,SAAS,EAAC;wBAAkC;0BAAAU,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpD,CAAC,eACNrF,OAAA;wBAAOwE,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAAC;sBAEvD;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNrF,OAAA;sBACEoH,KAAK,EAAEpE,aAAc;sBACrB2E,QAAQ,EAAGhB,CAAC,IAAKlD,kBAAkB,CAACkD,CAAC,CAACC,MAAM,CAACQ,KAAK,CAAE;sBACpDQ,WAAW,EAAC,0BAA0B;sBACtCpD,SAAS,EAAC,8KAA8K;sBACxLqD,IAAI,EAAC;oBAAG;sBAAA3C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA3HDjD,aAAa;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4HR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNrF,OAAA;MAAKwE,SAAS,EAAC,4GAA4G;MAAAC,QAAA,eACzHzE,OAAA;QAAKwE,SAAS,EAAC,6FAA6F;QAAAC,QAAA,eAC1GzE,OAAA;UAAKwE,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAE/DzE,OAAA;YACEuH,OAAO,EAAE3D,cAAe;YACxBkE,QAAQ,EAAE1F,aAAa,KAAK,CAAE;YAC9BoC,SAAS,EAAG,uLACVpC,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,6DACL,EAAE;YAAAqC,QAAA,gBAEHzE,OAAA,CAACT,WAAW;cAACiF,SAAS,EAAC;YAAuB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDrF,OAAA;cAAMwE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDrF,OAAA;cAAMwE,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAGTrF,OAAA;YAAKwE,SAAS,EAAC,uGAAuG;YAAAC,QAAA,EACnHsD,KAAK,CAACC,IAAI,CAAC;cAAEvG,MAAM,EAAEY;YAAe,CAAC,EAAE,CAAC4F,CAAC,EAAEC,KAAK,KAAK;cACpD,MAAMhF,UAAU,GAAGX,eAAe,CAAC2F,KAAK,CAAC,KAAKC,SAAS,IAAI5F,eAAe,CAAC2F,KAAK,CAAC,KAAK,EAAE;cACxF,MAAME,SAAS,GAAGF,KAAK,KAAK9F,aAAa;cAEzC,oBACEpC,OAAA;gBAEEwE,SAAS,EAAG,sIACV4D,SAAS,GACL,kCAAkC,GAClClF,UAAU,GACV,yBAAyB,GACzB,2BACL,EAAE;gBAAAuB,QAAA,EAEFvB,UAAU,IAAI,CAACkF,SAAS,gBACvBpI,OAAA,CAACP,OAAO;kBAAC+E,SAAS,EAAC;gBAAuB;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAE7C6C,KAAK,GAAG;cACT,GAbIA,KAAK;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcP,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNrF,OAAA;YACEuH,OAAO,EAAE5D,UAAW;YACpBa,SAAS,EAAC,8OAA8O;YAAAC,QAAA,gBAExPzE,OAAA;cAAMwE,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC/BrC,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,QAAQ,GAAG;YAAM;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACPrF,OAAA;cAAMwE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACxBrC,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,QAAQ,GAAG;YAAM;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,EACNjD,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCrC,OAAA,CAACP,OAAO;cAAC+E,SAAS,EAAC;YAAuB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE7CrF,OAAA,CAACR,YAAY;cAACgF,SAAS,EAAC;YAAuB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAClD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtC,EAAA,CA7VIZ,eAAe;AAAAkG,EAAA,GAAflG,eAAe;AA+VrB,eAAeA,eAAe;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}