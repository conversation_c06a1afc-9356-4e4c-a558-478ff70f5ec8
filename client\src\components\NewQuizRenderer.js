import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Tb<PERSON><PERSON>,
  TbArrowLeft,
  TbArrowRight,
  TbCheck,
  TbX,
  TbPhoto,
  TbEdit,
  TbCircleCheck,
  TbCircle
} from 'react-icons/tb';
import './NewQuizRenderer.css';

// Utility function to extract and normalize question data
const extractQuestionData = (question) => {
  if (!question) return null;

  const questionText = question.name || question.question || question.text || question.title || '';
  const questionType = question.type || question.answerType || question.questionType || 'mcq';
  const questionOptions = question.options || question.choices || question.answers || {};
  const correctAnswer = question.correctAnswer || question.correctOption || '';
  const imageUrl = question.imageUrl || question.image || '';

  // Normalize question type
  let normalizedType = 'mcq';
  const typeString = String(questionType).toLowerCase();

  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {
    normalizedType = 'mcq';
  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {
    normalizedType = 'fill';
  } else if (typeString.includes('image') || typeString.includes('picture') || typeString.includes('diagram')) {
    normalizedType = 'image';
  }

  // If no options are provided but it's marked as MCQ, treat as fill-in
  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {
    normalizedType = 'fill';
  }

  return {
    text: questionText,
    type: normalizedType,
    options: questionOptions,
    correctAnswer,
    imageUrl,
    originalQuestion: question
  };
};

// Format time utility
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const NewQuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  selectedOptions = {},
  onAnswerChange,
  timeLeft,
  onNext,
  onPrevious,
  examTitle = "Quiz",
  isTimeWarning = false,
  onSubmit
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);

  // BYPASS extractQuestionData - it's causing issues, use direct question data
  const questionData = null; // Force bypass to use fallback system

  // Debug logging
  useEffect(() => {
    console.log('🎯 NewQuizRenderer Debug:', {
      questionIndex,
      totalQuestions,
      hasQuestion: !!question,
      hasQuestionData: !!questionData,
      questionType: question?.type || question?.answerType,
      questionText: question?.name || question?.question,
      timeLeft
    });
  }, [question, questionData, questionIndex, totalQuestions, timeLeft]);

  // Update current answer when selectedAnswer changes
  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  // Handle answer selection
  const handleAnswerChange = useCallback((answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(!!answer);
    onAnswerChange(answer);
  }, [onAnswerChange]);

  // Handle navigation
  const handleNext = useCallback(() => {
    if (questionIndex === totalQuestions - 1) {
      // Last question - submit quiz
      if (onSubmit) {
        onSubmit();
      }
    } else {
      onNext();
    }
  }, [questionIndex, totalQuestions, onNext, onSubmit]);

  const handlePrevious = useCallback(() => {
    if (questionIndex > 0) {
      onPrevious();
    }
  }, [questionIndex, onPrevious]);

  // Always render - never show loading screen
  // Create safe question data with fallbacks - be more aggressive about showing content
  const safeQuestionData = {
    text: question?.name || question?.question || question?.text || "TEST QUESTION: What is 2 + 2?",
    type: question?.type || question?.answerType || 'mcq',
    options: question?.options || question?.choices || {
      A: question?.optionA || 'Option A: 3',
      B: question?.optionB || 'Option B: 4',
      C: question?.optionC || 'Option C: 5',
      D: question?.optionD || 'Option D: 6'
    },
    imageUrl: question?.imageUrl || question?.image || question?.diagram
  };

  // Debug what we're actually rendering
  console.log('🎯 NewQuizRenderer rendering:', {
    hasQuestion: !!question,
    hasQuestionData: !!questionData,
    safeQuestionData,
    questionIndex,
    totalQuestions,
    rawQuestion: question
  });

  return (
    <div style={{width: '100%', height: '100vh', background: 'lightblue', padding: '20px'}}>
      {/* DEBUG: Test if component renders */}
      <div style={{position: 'fixed', top: '10px', right: '10px', background: 'red', color: 'white', padding: '10px', zIndex: 9999}}>
        QUIZ RENDERER ACTIVE - Q{questionIndex + 1}/{totalQuestions}
      </div>

      {/* SIMPLE HEADER */}
      <div style={{background: 'white', padding: '20px', marginBottom: '20px'}}>
        <h1 style={{fontSize: '24px', fontWeight: 'bold'}}>{examTitle}</h1>
        <p>Question {questionIndex + 1} of {totalQuestions}</p>
        <p>Time: {formatTime(timeLeft)}</p>
      </div>

      {/* SIMPLE QUESTION */}
      <div style={{background: 'white', padding: '30px', marginBottom: '20px'}}>
        <h2 style={{fontSize: '20px', marginBottom: '20px'}}>{safeQuestionData.text}</h2>

        {safeQuestionData.options && Object.entries(safeQuestionData.options).map(([key, value]) => (
          <div
            key={key}
            onClick={() => handleAnswerChange(key)}
            style={{
              background: currentAnswer === key ? 'lightgreen' : 'lightgray',
              padding: '15px',
              margin: '10px 0',
              cursor: 'pointer',
              border: '2px solid black'
            }}
          >
            {key}: {value}
          </div>
        ))}
      </div>

      {/* SIMPLE NAVIGATION */}
      <div style={{background: 'white', padding: '20px'}}>
        <button
          onClick={onPrevious}
          disabled={questionIndex === 0}
          style={{padding: '10px 20px', marginRight: '10px', background: 'gray', color: 'white'}}
        >
          Previous
        </button>
        <button
          onClick={onNext}
          style={{padding: '10px 20px', background: 'blue', color: 'white'}}
        >
          {questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'}
        </button>
      </div>
    </div>
  );
};

export default NewQuizRenderer;
