import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Tb<PERSON><PERSON>,
  TbArrowLeft,
  TbArrowRight,
  TbCheck,
  TbX,
  TbPhoto,
  TbEdit,
  TbCircleCheck,
  TbCircle
} from 'react-icons/tb';
import './NewQuizRenderer.css';

// Utility function to extract and normalize question data
const extractQuestionData = (question) => {
  if (!question) return null;

  const questionText = question.name || question.question || question.text || question.title || '';
  const questionType = question.type || question.answerType || question.questionType || 'mcq';
  const questionOptions = question.options || question.choices || question.answers || {};
  const correctAnswer = question.correctAnswer || question.correctOption || '';
  const imageUrl = question.imageUrl || question.image || '';

  // Normalize question type
  let normalizedType = 'mcq';
  const typeString = String(questionType).toLowerCase();

  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {
    normalizedType = 'mcq';
  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {
    normalizedType = 'fill';
  } else if (typeString.includes('image') || typeString.includes('picture') || typeString.includes('diagram')) {
    normalizedType = 'image';
  }

  // If no options are provided but it's marked as MCQ, treat as fill-in
  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {
    normalizedType = 'fill';
  }

  return {
    text: questionText,
    type: normalizedType,
    options: questionOptions,
    correctAnswer,
    imageUrl,
    originalQuestion: question
  };
};

// Format time utility
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const NewQuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  selectedOptions = {},
  onAnswerChange,
  timeLeft,
  onNext,
  onPrevious,
  examTitle = "Quiz",
  isTimeWarning = false,
  onSubmit
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);

  const questionData = question ? extractQuestionData(question) : null;

  // Debug logging
  useEffect(() => {
    console.log('🎯 NewQuizRenderer Debug:', {
      questionIndex,
      totalQuestions,
      hasQuestion: !!question,
      hasQuestionData: !!questionData,
      questionType: question?.type || question?.answerType,
      questionText: question?.name || question?.question,
      timeLeft
    });
  }, [question, questionData, questionIndex, totalQuestions, timeLeft]);

  // Update current answer when selectedAnswer changes
  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  // Handle answer selection
  const handleAnswerChange = useCallback((answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(!!answer);
    onAnswerChange(answer);
  }, [onAnswerChange]);

  // Handle navigation
  const handleNext = useCallback(() => {
    if (questionIndex === totalQuestions - 1) {
      // Last question - submit quiz
      if (onSubmit) {
        onSubmit();
      }
    } else {
      onNext();
    }
  }, [questionIndex, totalQuestions, onNext, onSubmit]);

  const handlePrevious = useCallback(() => {
    if (questionIndex > 0) {
      onPrevious();
    }
  }, [questionIndex, onPrevious]);

  // Show loading state if no question data yet
  if (!question || !questionData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        {/* Header with Timer and Quiz Info */}
        <div className="bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
            <div className="flex items-center justify-between gap-2 sm:gap-4">
              {/* Quiz Title and Question Counter */}
              <div className="flex-1 min-w-0">
                <h1 className="text-base sm:text-lg lg:text-xl font-bold text-gray-900 truncate">
                  {examTitle}
                </h1>
                <p className="text-xs sm:text-sm text-gray-600 mt-1">
                  Loading questions...
                </p>
              </div>

              {/* Timer */}
              <div className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-2 rounded-full font-bold text-xs sm:text-sm lg:text-base transition-all ${
                isTimeWarning
                  ? 'bg-red-100 text-red-700 animate-pulse'
                  : 'bg-blue-100 text-blue-700'
              }`}>
                <TbClock className="w-4 h-4 sm:w-5 sm:h-5" />
                <span>{formatTime(timeLeft)}</span>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mt-3 sm:mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-gray-300 h-2 rounded-full w-1/4 animate-pulse" />
              </div>
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>Loading...</span>
                <span>Please wait</span>
              </div>
            </div>
          </div>
        </div>

        {/* Loading Content */}
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 sm:h-16 sm:w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-lg sm:text-xl font-bold text-gray-900 mb-2">Loading Quiz</h2>
            <p className="text-sm sm:text-base text-gray-600">Please wait while we prepare your questions...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container">
      {/* Header with Timer and Quiz Info */}
      <div className="bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4">
          <div className="flex items-center justify-between gap-2 sm:gap-4">
            {/* Quiz Title and Question Counter */}
            <div className="flex-1 min-w-0">
              <h1 className="text-base sm:text-lg lg:text-xl font-bold text-gray-900 truncate">
                {examTitle}
              </h1>
              <p className="text-xs sm:text-sm text-gray-600 mt-1">
                Question {questionIndex + 1} of {totalQuestions}
              </p>
            </div>

            {/* Timer */}
            <div className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-2 rounded-full font-bold text-xs sm:text-sm lg:text-base transition-all ${
              isTimeWarning
                ? 'bg-red-100 text-red-700 animate-pulse'
                : 'bg-blue-100 text-blue-700'
            }`}>
              <TbClock className="w-4 h-4 sm:w-5 sm:h-5" />
              <span>{formatTime(timeLeft)}</span>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-3 sm:mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <motion.div
                className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full progress-bar"
                initial={{ width: 0 }}
                animate={{ width: `${((questionIndex + 1) / totalQuestions) * 100}%` }}
                transition={{ duration: 0.5, ease: "easeOut" }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Progress</span>
              <span>{Math.round(((questionIndex + 1) / totalQuestions) * 100)}%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Scrollable */}
      <div className="flex-1 overflow-y-auto quiz-content">
        <div className="max-w-4xl tablet-layout desktop-layout mx-auto w-full px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8 pb-24">
          <AnimatePresence mode="wait">
            <motion.div
              key={questionIndex}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-200 overflow-hidden"
            >
              {/* Question Header */}
              <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-4 sm:px-6 py-3 sm:py-4">
                <div className="flex items-center justify-between gap-2">
                  <div className={`px-2 sm:px-3 py-1 rounded-full text-xs font-semibold ${
                    questionData.type === 'mcq'
                      ? 'bg-blue-100 text-blue-800'
                      : questionData.type === 'fill'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-purple-100 text-purple-800'
                  }`}>
                    {questionData.type === 'mcq' ? 'Multiple Choice' :
                     questionData.type === 'fill' ? 'Fill in the Blank' :
                     'Image Question'}
                  </div>
                  {isAnswered && (
                    <div className="flex items-center gap-1 text-white text-xs sm:text-sm font-medium">
                      <TbCheck className="w-3 h-3 sm:w-4 sm:h-4" />
                      <span className="hidden sm:inline">Answered</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Question Content */}
              <div className="p-4 sm:p-6 lg:p-8">
                {/* Question Text */}
                <div className="mb-6 sm:mb-8">
                  <h2 className="quiz-question-text text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 mb-4 leading-relaxed">
                    {questionData.text}
                  </h2>

                  {/* Image for image-based questions */}
                  {questionData.imageUrl && (
                    <div className="mb-6">
                      <div className="relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200">
                        <img
                          src={questionData.imageUrl}
                          alt="Question diagram"
                          className="quiz-image w-full max-w-full sm:max-w-lg lg:max-w-2xl mx-auto h-auto object-contain max-h-64 sm:max-h-80 lg:max-h-96"
                          style={{ display: 'block' }}
                          onLoad={(e) => {
                            e.target.style.display = 'block';
                            const fallback = e.target.parentNode.querySelector('.image-fallback');
                            if (fallback) fallback.style.display = 'none';
                          }}
                          onError={(e) => {
                            e.target.style.display = 'none';
                            const fallback = e.target.parentNode.querySelector('.image-fallback');
                            if (fallback) fallback.style.display = 'flex';
                          }}
                        />
                        <div className="image-fallback hidden items-center justify-center h-32 sm:h-48 text-gray-500">
                          <div className="text-center">
                            <TbPhoto className="w-8 h-8 sm:w-12 sm:h-12 mx-auto mb-2" />
                            <p className="text-sm sm:text-base">Image not available</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Answer Options */}
                <div className="space-y-3 sm:space-y-4">
                  {questionData.type === 'mcq' && questionData.options && Object.keys(questionData.options).length > 0 ? (
                    // Multiple Choice Questions
                    Object.entries(questionData.options).map(([key, value]) => {
                      const isSelected = currentAnswer === key;
                      return (
                        <motion.button
                          key={key}
                          onClick={() => handleAnswerChange(key)}
                          className={`quiz-option-button quiz-button w-full p-3 sm:p-4 rounded-lg sm:rounded-xl border-2 text-left transition-all duration-200 ${
                            isSelected
                              ? 'border-blue-500 bg-blue-50 shadow-md'
                              : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'
                          }`}
                          whileHover={{ scale: 1.01 }}
                          whileTap={{ scale: 0.99 }}
                        >
                          <div className="flex items-start gap-3 sm:gap-4">
                            <div className={`w-7 h-7 sm:w-8 sm:h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm flex-shrink-0 mt-0.5 ${
                              isSelected
                                ? 'border-blue-500 bg-blue-500 text-white'
                                : 'border-gray-300 text-gray-600'
                            }`}>
                              {isSelected ? <TbCheck className="w-4 h-4 sm:w-5 sm:h-5" /> : key}
                            </div>
                            <span className="quiz-option-text text-gray-900 font-medium text-sm sm:text-base leading-relaxed">{value}</span>
                          </div>
                        </motion.button>
                      );
                    })
                  ) : (
                    // Fill-in-the-Blank Questions
                    <div className="space-y-4">
                      <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg sm:rounded-xl p-4 sm:p-6 border-2 border-green-200">
                        <div className="flex items-center gap-2 mb-4">
                          <div className="w-7 h-7 sm:w-8 sm:h-8 bg-green-500 rounded-full flex items-center justify-center">
                            <TbEdit className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                          </div>
                          <label className="text-sm font-semibold text-gray-700">
                            Your Answer:
                          </label>
                        </div>
                        <textarea
                          value={currentAnswer}
                          onChange={(e) => handleAnswerChange(e.target.value)}
                          placeholder="Type your answer here..."
                          className="w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base"
                          rows="3"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      {/* Navigation Footer */}
      <div className="quiz-footer bg-white border-t-2 border-gray-200 shadow-lg sticky bottom-0 z-50">
        <div className="max-w-4xl tablet-layout desktop-layout mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4 mobile-nav">
          <div className="flex items-center justify-between gap-2 sm:gap-4">
            {/* Previous Button */}
            <button
              onClick={handlePrevious}
              disabled={questionIndex === 0}
              className={`quiz-nav-button quiz-button mobile-nav-button flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base ${
                questionIndex === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'
              }`}
            >
              <TbArrowLeft className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="hidden sm:inline">Previous</span>
              <span className="sm:hidden">Prev</span>
            </button>

            {/* Question Navigation Dots */}
            <div className="flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-[200px] sm:max-w-xs lg:max-w-md scrollbar-hide">
              {Array.from({ length: totalQuestions }, (_, index) => {
                const isAnswered = selectedOptions[index] !== undefined && selectedOptions[index] !== '';
                const isCurrent = index === questionIndex;

                return (
                  <div
                    key={index}
                    className={`quiz-nav-button w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${
                      isCurrent
                        ? 'bg-blue-600 text-white scale-110'
                        : isAnswered
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 text-gray-600'
                    }`}
                  >
                    {isAnswered && !isCurrent ? (
                      <TbCheck className="w-3 h-3 sm:w-4 sm:h-4" />
                    ) : (
                      index + 1
                    )}
                  </div>
                );
              })}
            </div>

            {/* Next/Submit Button */}
            <button
              onClick={handleNext}
              className="quiz-nav-button quiz-button mobile-nav-button flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg sm:rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base"
            >
              <span className="hidden sm:inline">
                {questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'}
              </span>
              <span className="sm:hidden">
                {questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'}
              </span>
              {questionIndex === totalQuestions - 1 ? (
                <TbCheck className="w-4 h-4 sm:w-5 sm:h-5" />
              ) : (
                <TbArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewQuizRenderer;
