import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Tb<PERSON><PERSON>,
  TbArrowLeft,
  TbArrowRight,
  TbCheck,
  TbX,
  TbPhoto,
  TbEdit,
  TbCircleCheck,
  TbCircle
} from 'react-icons/tb';
import './NewQuizRenderer.css';

// Utility function to extract and normalize question data
const extractQuestionData = (question) => {
  if (!question) return null;

  const questionText = question.name || question.question || question.text || question.title || '';
  const questionType = question.type || question.answerType || question.questionType || 'mcq';
  const questionOptions = question.options || question.choices || question.answers || {};
  const correctAnswer = question.correctAnswer || question.correctOption || '';
  const imageUrl = question.imageUrl || question.image || '';

  // Normalize question type
  let normalizedType = 'mcq';
  const typeString = String(questionType).toLowerCase();

  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {
    normalizedType = 'mcq';
  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {
    normalizedType = 'fill';
  } else if (typeString.includes('image') || typeString.includes('picture') || typeString.includes('diagram')) {
    normalizedType = 'image';
  }

  // If no options are provided but it's marked as MCQ, treat as fill-in
  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {
    normalizedType = 'fill';
  }

  return {
    text: questionText,
    type: normalizedType,
    options: questionOptions,
    correctAnswer,
    imageUrl,
    originalQuestion: question
  };
};

// Format time utility
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

const NewQuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  selectedOptions = {},
  onAnswerChange,
  timeLeft,
  onNext,
  onPrevious,
  examTitle = "Quiz",
  isTimeWarning = false,
  onSubmit
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);

  // BYPASS extractQuestionData - it's causing issues, use direct question data
  const questionData = null; // Force bypass to use fallback system

  // Debug logging
  useEffect(() => {
    console.log('🎯 NewQuizRenderer Debug:', {
      questionIndex,
      totalQuestions,
      hasQuestion: !!question,
      hasQuestionData: !!questionData,
      questionType: question?.type || question?.answerType,
      questionText: question?.name || question?.question,
      timeLeft
    });
  }, [question, questionData, questionIndex, totalQuestions, timeLeft]);

  // Update current answer when selectedAnswer changes
  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  // Handle answer selection
  const handleAnswerChange = useCallback((answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(!!answer);
    onAnswerChange(answer);
  }, [onAnswerChange]);

  // Handle navigation
  const handleNext = useCallback(() => {
    if (questionIndex === totalQuestions - 1) {
      // Last question - submit quiz
      if (onSubmit) {
        onSubmit();
      }
    } else {
      onNext();
    }
  }, [questionIndex, totalQuestions, onNext, onSubmit]);

  const handlePrevious = useCallback(() => {
    if (questionIndex > 0) {
      onPrevious();
    }
  }, [questionIndex, onPrevious]);

  // Always render - never show loading screen
  // Create safe question data with fallbacks - be more aggressive about showing content
  const safeQuestionData = {
    text: question?.name || question?.question || question?.text || "TEST QUESTION: What is 2 + 2?",
    type: question?.type || question?.answerType || 'mcq',
    options: question?.options || question?.choices || {
      A: question?.optionA || 'Option A: 3',
      B: question?.optionB || 'Option B: 4',
      C: question?.optionC || 'Option C: 5',
      D: question?.optionD || 'Option D: 6'
    },
    imageUrl: question?.imageUrl || question?.image || question?.diagram
  };

  // Debug what we're actually rendering
  console.log('🎯 NewQuizRenderer rendering:', {
    hasQuestion: !!question,
    hasQuestionData: !!questionData,
    safeQuestionData,
    questionIndex,
    totalQuestions,
    rawQuestion: question
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header with Timer and Quiz Info */}
      <div className="bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Quiz Title and Question Counter */}
            <div className="flex-1">
              <h1 className="text-xl font-bold text-gray-900 truncate">
                {examTitle}
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                Question {questionIndex + 1} of {totalQuestions}
              </p>
            </div>

            {/* Timer */}
            <div className={`flex items-center gap-2 px-4 py-2 rounded-full font-bold text-base transition-all ${
              isTimeWarning
                ? 'bg-red-100 text-red-700 animate-pulse'
                : 'bg-blue-100 text-blue-700'
            }`}>
              <TbClock className="w-5 h-5" />
              <span>{formatTime(timeLeft)}</span>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-500"
                style={{ width: `${((questionIndex + 1) / totalQuestions) * 100}%` }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Progress</span>
              <span>{Math.round(((questionIndex + 1) / totalQuestions) * 100)}%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-6">
        <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
          {/* Question Header */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className={`px-3 py-1 rounded-full text-xs font-semibold ${
                safeQuestionData.type === 'mcq'
                  ? 'bg-blue-100 text-blue-800'
                  : safeQuestionData.type === 'fill'
                  ? 'bg-green-100 text-green-800'
                  : 'bg-purple-100 text-purple-800'
              }`}>
                {safeQuestionData.type === 'mcq' ? 'Multiple Choice' :
                 safeQuestionData.type === 'fill' ? 'Fill in the Blank' :
                 'Image Question'}
              </div>
              {currentAnswer && (
                <div className="flex items-center gap-1 text-white text-sm font-medium">
                  <TbCheck className="w-4 h-4" />
                  Answered
                </div>
              )}
            </div>
          </div>

          {/* Question Content */}
          <div className="p-6 max-h-[60vh] overflow-y-auto">
            {/* Question Text */}
            <div className="mb-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 leading-relaxed">
                {safeQuestionData.text}
              </h2>

              {/* Image Display */}
              {safeQuestionData.imageUrl && (
                <div className="mb-6">
                  <div className="relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200">
                    <img
                      src={safeQuestionData.imageUrl}
                      alt="Question diagram"
                      className="w-full max-w-2xl mx-auto h-auto object-contain max-h-80"
                      style={{ display: 'block' }}
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                    <div className="hidden items-center justify-center h-48 text-gray-500">
                      <div className="text-center">
                        <TbPhoto className="w-12 h-12 mx-auto mb-2" />
                        <p>Image not available</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Answer Options */}
            <div className="space-y-4">
              {/* Multiple Choice Questions */}
              {safeQuestionData.type === 'mcq' && safeQuestionData.options && Object.entries(safeQuestionData.options).map(([key, value]) => {
                const isSelected = currentAnswer === key;
                return (
                  <button
                    key={key}
                    onClick={() => handleAnswerChange(key)}
                    className={`w-full p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                      isSelected
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'
                    }`}
                  >
                    <div className="flex items-center gap-4">
                      <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold ${
                        isSelected
                          ? 'border-blue-500 bg-blue-500 text-white'
                          : 'border-gray-300 text-gray-600'
                      }`}>
                        {isSelected ? <TbCheck className="w-5 h-5" /> : key}
                      </div>
                      <span className="text-gray-900 font-medium">{value}</span>
                    </div>
                  </button>
                );
              })}

              {/* Fill-in-the-Blank Questions */}
              {safeQuestionData.type === 'fill' && (
                <div className="space-y-4">
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border-2 border-green-200">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <TbEdit className="w-5 h-5 text-white" />
                      </div>
                      <label className="text-sm font-semibold text-gray-700">
                        Your Answer:
                      </label>
                    </div>
                    <textarea
                      value={currentAnswer}
                      onChange={(e) => handleAnswerChange(e.target.value)}
                      placeholder="Type your answer here..."
                      className="w-full p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all"
                      rows="4"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Footer */}
      <div className="bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between gap-4">
            {/* Previous Button */}
            <button
              onClick={onPrevious}
              disabled={questionIndex === 0}
              className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all ${
                questionIndex === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'
              }`}
            >
              <TbArrowLeft className="w-5 h-5" />
              <span>Previous</span>
            </button>

            {/* Question Navigation Dots */}
            <div className="flex items-center gap-2 overflow-x-auto max-w-md">
              {Array.from({ length: totalQuestions }, (_, index) => {
                const isAnswered = selectedOptions[index] !== undefined && selectedOptions[index] !== '';
                const isCurrent = index === questionIndex;

                return (
                  <div
                    key={index}
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all ${
                      isCurrent
                        ? 'bg-blue-600 text-white scale-110'
                        : isAnswered
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 text-gray-600'
                    }`}
                  >
                    {isAnswered && !isCurrent ? (
                      <TbCheck className="w-4 h-4" />
                    ) : (
                      index + 1
                    )}
                  </div>
                );
              })}
            </div>

            {/* Next/Submit Button */}
            <button
              onClick={onNext}
              className="flex items-center gap-2 px-6 py-3 rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md"
            >
              <span>
                {questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'}
              </span>
              {questionIndex === totalQuestions - 1 ? (
                <TbCheck className="w-5 h-5" />
              ) : (
                <TbArrowRight className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewQuizRenderer;
