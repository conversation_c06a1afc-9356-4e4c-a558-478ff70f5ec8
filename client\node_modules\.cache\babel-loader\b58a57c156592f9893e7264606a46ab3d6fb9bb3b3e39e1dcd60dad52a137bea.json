{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\NewQuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck, TbX, TbPhoto, TbEdit, TbCircleCheck, TbCircle } from 'react-icons/tb';\nimport './NewQuizRenderer.css';\n\n// Utility function to extract and normalize question data\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst extractQuestionData = question => {\n  if (!question) return null;\n  const questionText = question.name || question.question || question.text || question.title || '';\n  const questionType = question.type || question.answerType || question.questionType || 'mcq';\n  const questionOptions = question.options || question.choices || question.answers || {};\n  const correctAnswer = question.correctAnswer || question.correctOption || '';\n  const imageUrl = question.imageUrl || question.image || '';\n\n  // Normalize question type\n  let normalizedType = 'mcq';\n  const typeString = String(questionType).toLowerCase();\n  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {\n    normalizedType = 'mcq';\n  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {\n    normalizedType = 'fill';\n  } else if (typeString.includes('image') || typeString.includes('picture') || typeString.includes('diagram')) {\n    normalizedType = 'image';\n  }\n\n  // If no options are provided but it's marked as MCQ, treat as fill-in\n  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {\n    normalizedType = 'fill';\n  }\n  return {\n    text: questionText,\n    type: normalizedType,\n    options: questionOptions,\n    correctAnswer,\n    imageUrl,\n    originalQuestion: question\n  };\n};\n\n// Format time utility\nconst formatTime = seconds => {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n};\nconst NewQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false,\n  onSubmit\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const questionData = question ? extractQuestionData(question) : null;\n\n  // Debug logging\n  useEffect(() => {\n    console.log('🎯 NewQuizRenderer Debug:', {\n      questionIndex,\n      totalQuestions,\n      hasQuestion: !!question,\n      hasQuestionData: !!questionData,\n      questionType: (question === null || question === void 0 ? void 0 : question.type) || (question === null || question === void 0 ? void 0 : question.answerType),\n      questionText: (question === null || question === void 0 ? void 0 : question.name) || (question === null || question === void 0 ? void 0 : question.question),\n      timeLeft\n    });\n  }, [question, questionData, questionIndex, totalQuestions, timeLeft]);\n\n  // Update current answer when selectedAnswer changes\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Handle answer selection\n  const handleAnswerChange = useCallback(answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(!!answer);\n    onAnswerChange(answer);\n  }, [onAnswerChange]);\n\n  // Handle navigation\n  const handleNext = useCallback(() => {\n    if (questionIndex === totalQuestions - 1) {\n      // Last question - submit quiz\n      if (onSubmit) {\n        onSubmit();\n      }\n    } else {\n      onNext();\n    }\n  }, [questionIndex, totalQuestions, onNext, onSubmit]);\n  const handlePrevious = useCallback(() => {\n    if (questionIndex > 0) {\n      onPrevious();\n    }\n  }, [questionIndex, onPrevious]);\n\n  // More lenient loading check - only show loading if absolutely no data\n  if (!question && !questionData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between gap-2 sm:gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1 min-w-0\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-base sm:text-lg lg:text-xl font-bold text-gray-900 truncate\",\n                children: examTitle\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs sm:text-sm text-gray-600 mt-1\",\n                children: \"Loading questions...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-2 rounded-full font-bold text-xs sm:text-sm lg:text-base transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 animate-pulse' : 'bg-blue-100 text-blue-700'}`,\n              children: [/*#__PURE__*/_jsxDEV(TbClock, {\n                className: \"w-4 h-4 sm:w-5 sm:h-5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: formatTime(timeLeft)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3 sm:mt-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-200 rounded-full h-2\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gray-300 h-2 rounded-full w-1/4 animate-pulse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex justify-between text-xs text-gray-500 mt-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Please wait\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 sm:h-16 sm:w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-lg sm:text-xl font-bold text-gray-900 mb-2\",\n            children: \"Loading Quiz\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm sm:text-base text-gray-600\",\n            children: \"Please wait while we prepare your questions...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Fallback question data if question exists but questionData is missing\n  const safeQuestionData = questionData || {\n    text: (question === null || question === void 0 ? void 0 : question.name) || (question === null || question === void 0 ? void 0 : question.question) || \"Question loading...\",\n    type: (question === null || question === void 0 ? void 0 : question.type) || 'mcq',\n    options: (question === null || question === void 0 ? void 0 : question.options) || {},\n    imageUrl: (question === null || question === void 0 ? void 0 : question.imageUrl) || (question === null || question === void 0 ? void 0 : question.image)\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"h-screen flex flex-col bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between gap-2 sm:gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-base sm:text-lg lg:text-xl font-bold text-gray-900 truncate\",\n              children: examTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs sm:text-sm text-gray-600 mt-1\",\n              children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-2 rounded-full font-bold text-xs sm:text-sm lg:text-base transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 animate-pulse' : 'bg-blue-100 text-blue-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 sm:mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full progress-bar\",\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${(questionIndex + 1) / totalQuestions * 100}%`\n              },\n              transition: {\n                duration: 0.5,\n                ease: \"easeOut\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-xs text-gray-500 mt-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [Math.round((questionIndex + 1) / totalQuestions * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto quiz-content\",\n      style: {\n        height: 'calc(100vh - 140px)'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl tablet-layout desktop-layout mx-auto w-full px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8\",\n        children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n          mode: \"wait\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: 20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            exit: {\n              opacity: 0,\n              x: -20\n            },\n            transition: {\n              duration: 0.3\n            },\n            className: \"bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-200 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-blue-600 to-indigo-700 px-4 sm:px-6 py-3 sm:py-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between gap-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `px-2 sm:px-3 py-1 rounded-full text-xs font-semibold ${safeQuestionData.type === 'mcq' ? 'bg-blue-100 text-blue-800' : safeQuestionData.type === 'fill' ? 'bg-green-100 text-green-800' : 'bg-purple-100 text-purple-800'}`,\n                  children: safeQuestionData.type === 'mcq' ? 'Multiple Choice' : safeQuestionData.type === 'fill' ? 'Fill in the Blank' : 'Image Question'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this), isAnswered && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-1 text-white text-xs sm:text-sm font-medium\",\n                  children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                    className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"hidden sm:inline\",\n                    children: \"Answered\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 sm:p-6 lg:p-8 max-h-[calc(100vh-300px)] overflow-y-auto\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6 sm:mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"quiz-question-text text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 mb-4 leading-relaxed\",\n                  children: safeQuestionData.text\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 19\n                }, this), safeQuestionData.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-6\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: safeQuestionData.imageUrl,\n                      alt: \"Question diagram\",\n                      className: \"quiz-image w-full max-w-full sm:max-w-lg lg:max-w-2xl mx-auto h-auto object-contain max-h-64 sm:max-h-80 lg:max-h-96\",\n                      style: {\n                        display: 'block'\n                      },\n                      onLoad: e => {\n                        e.target.style.display = 'block';\n                        const fallback = e.target.parentNode.querySelector('.image-fallback');\n                        if (fallback) fallback.style.display = 'none';\n                      },\n                      onError: e => {\n                        e.target.style.display = 'none';\n                        const fallback = e.target.parentNode.querySelector('.image-fallback');\n                        if (fallback) fallback.style.display = 'flex';\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"image-fallback hidden items-center justify-center h-32 sm:h-48 text-gray-500\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-center\",\n                        children: [/*#__PURE__*/_jsxDEV(TbPhoto, {\n                          className: \"w-8 h-8 sm:w-12 sm:h-12 mx-auto mb-2\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 295,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm sm:text-base\",\n                          children: \"Image not available\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 296,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 294,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3 sm:space-y-4\",\n                children: safeQuestionData.type === 'mcq' && safeQuestionData.options && Object.keys(safeQuestionData.options).length > 0 ?\n                // Multiple Choice Questions\n                Object.entries(safeQuestionData.options).map(([key, value]) => {\n                  const isSelected = currentAnswer === key;\n                  return /*#__PURE__*/_jsxDEV(motion.button, {\n                    onClick: () => handleAnswerChange(key),\n                    className: `quiz-option-button quiz-button w-full p-3 sm:p-4 rounded-lg sm:rounded-xl border-2 text-left transition-all duration-200 ${isSelected ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'}`,\n                    whileHover: {\n                      scale: 1.01\n                    },\n                    whileTap: {\n                      scale: 0.99\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-start gap-3 sm:gap-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: `w-7 h-7 sm:w-8 sm:h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm flex-shrink-0 mt-0.5 ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-600'}`,\n                        children: isSelected ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                          className: \"w-4 h-4 sm:w-5 sm:h-5\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 328,\n                          columnNumber: 45\n                        }, this) : key\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 323,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"quiz-option-text text-gray-900 font-medium text-sm sm:text-base leading-relaxed break-words\",\n                        children: value\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 330,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 27\n                    }, this)\n                  }, key, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 311,\n                    columnNumber: 25\n                  }, this);\n                }) :\n                /*#__PURE__*/\n                // Fill-in-the-Blank Questions\n                _jsxDEV(\"div\", {\n                  className: \"space-y-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg sm:rounded-xl p-4 sm:p-6 border-2 border-green-200\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-2 mb-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-7 h-7 sm:w-8 sm:h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(TbEdit, {\n                          className: \"w-4 h-4 sm:w-5 sm:h-5 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 341,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 340,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                        className: \"text-sm font-semibold text-gray-700\",\n                        children: \"Your Answer:\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 343,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 339,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      value: currentAnswer,\n                      onChange: e => handleAnswerChange(e.target.value),\n                      placeholder: \"Type your answer here...\",\n                      className: \"w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base\",\n                      rows: \"4\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 338,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)]\n          }, questionIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"quiz-footer bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50 flex-shrink-0\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl tablet-layout desktop-layout mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4 mobile-nav\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between gap-2 sm:gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handlePrevious,\n            disabled: questionIndex === 0,\n            className: `quiz-nav-button quiz-button mobile-nav-button flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sm:hidden\",\n              children: \"Prev\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-[200px] sm:max-w-xs lg:max-w-md scrollbar-hide\",\n            children: Array.from({\n              length: totalQuestions\n            }, (_, index) => {\n              const isAnswered = selectedOptions[index] !== undefined && selectedOptions[index] !== '';\n              const isCurrent = index === questionIndex;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `quiz-nav-button w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${isCurrent ? 'bg-blue-600 text-white scale-110' : isAnswered ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600'}`,\n                children: isAnswered && !isCurrent ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-3 h-3 sm:w-4 sm:h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 23\n                }, this) : index + 1\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleNext,\n            className: \"quiz-nav-button quiz-button mobile-nav-button flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg sm:rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"sm:hidden\",\n              children: questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this), questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-4 h-4 sm:w-5 sm:h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 186,\n    columnNumber: 5\n  }, this);\n};\n_s(NewQuizRenderer, \"/AKwHDMDUIl41hZRNod4g0BrOFc=\");\n_c = NewQuizRenderer;\nexport default NewQuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"NewQuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "AnimatePresence", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbX", "TbPhoto", "TbEdit", "TbCircleCheck", "TbCircle", "jsxDEV", "_jsxDEV", "extractQuestionData", "question", "questionText", "name", "text", "title", "questionType", "type", "answerType", "questionOptions", "options", "choices", "answers", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "imageUrl", "image", "normalizedType", "typeString", "String", "toLowerCase", "includes", "Object", "keys", "length", "originalQuestion", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "selectedOptions", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "onSubmit", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "questionData", "console", "log", "hasQuestion", "hasQuestionData", "handleAnswerChange", "answer", "handleNext", "handlePrevious", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "safeQuestionData", "div", "initial", "width", "animate", "transition", "duration", "ease", "round", "style", "height", "mode", "opacity", "x", "exit", "src", "alt", "display", "onLoad", "e", "target", "fallback", "parentNode", "querySelector", "onError", "entries", "map", "key", "value", "isSelected", "button", "onClick", "whileHover", "scale", "whileTap", "onChange", "placeholder", "rows", "disabled", "Array", "from", "_", "index", "undefined", "isCurrent", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/NewQuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Tb<PERSON><PERSON>,\n  TbArrowLeft,\n  TbArrowRight,\n  TbCheck,\n  TbX,\n  TbPhoto,\n  TbEdit,\n  TbCircleCheck,\n  TbCircle\n} from 'react-icons/tb';\nimport './NewQuizRenderer.css';\n\n// Utility function to extract and normalize question data\nconst extractQuestionData = (question) => {\n  if (!question) return null;\n\n  const questionText = question.name || question.question || question.text || question.title || '';\n  const questionType = question.type || question.answerType || question.questionType || 'mcq';\n  const questionOptions = question.options || question.choices || question.answers || {};\n  const correctAnswer = question.correctAnswer || question.correctOption || '';\n  const imageUrl = question.imageUrl || question.image || '';\n\n  // Normalize question type\n  let normalizedType = 'mcq';\n  const typeString = String(questionType).toLowerCase();\n\n  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {\n    normalizedType = 'mcq';\n  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {\n    normalizedType = 'fill';\n  } else if (typeString.includes('image') || typeString.includes('picture') || typeString.includes('diagram')) {\n    normalizedType = 'image';\n  }\n\n  // If no options are provided but it's marked as MCQ, treat as fill-in\n  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {\n    normalizedType = 'fill';\n  }\n\n  return {\n    text: questionText,\n    type: normalizedType,\n    options: questionOptions,\n    correctAnswer,\n    imageUrl,\n    originalQuestion: question\n  };\n};\n\n// Format time utility\nconst formatTime = (seconds) => {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n};\n\nconst NewQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false,\n  onSubmit\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  const questionData = question ? extractQuestionData(question) : null;\n\n  // Debug logging\n  useEffect(() => {\n    console.log('🎯 NewQuizRenderer Debug:', {\n      questionIndex,\n      totalQuestions,\n      hasQuestion: !!question,\n      hasQuestionData: !!questionData,\n      questionType: question?.type || question?.answerType,\n      questionText: question?.name || question?.question,\n      timeLeft\n    });\n  }, [question, questionData, questionIndex, totalQuestions, timeLeft]);\n\n  // Update current answer when selectedAnswer changes\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Handle answer selection\n  const handleAnswerChange = useCallback((answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(!!answer);\n    onAnswerChange(answer);\n  }, [onAnswerChange]);\n\n  // Handle navigation\n  const handleNext = useCallback(() => {\n    if (questionIndex === totalQuestions - 1) {\n      // Last question - submit quiz\n      if (onSubmit) {\n        onSubmit();\n      }\n    } else {\n      onNext();\n    }\n  }, [questionIndex, totalQuestions, onNext, onSubmit]);\n\n  const handlePrevious = useCallback(() => {\n    if (questionIndex > 0) {\n      onPrevious();\n    }\n  }, [questionIndex, onPrevious]);\n\n  // More lenient loading check - only show loading if absolutely no data\n  if (!question && !questionData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n        {/* Header with Timer and Quiz Info */}\n        <div className=\"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\">\n          <div className=\"max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\">\n            <div className=\"flex items-center justify-between gap-2 sm:gap-4\">\n              {/* Quiz Title and Question Counter */}\n              <div className=\"flex-1 min-w-0\">\n                <h1 className=\"text-base sm:text-lg lg:text-xl font-bold text-gray-900 truncate\">\n                  {examTitle}\n                </h1>\n                <p className=\"text-xs sm:text-sm text-gray-600 mt-1\">\n                  Loading questions...\n                </p>\n              </div>\n\n              {/* Timer */}\n              <div className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-2 rounded-full font-bold text-xs sm:text-sm lg:text-base transition-all ${\n                isTimeWarning\n                  ? 'bg-red-100 text-red-700 animate-pulse'\n                  : 'bg-blue-100 text-blue-700'\n              }`}>\n                <TbClock className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n                <span>{formatTime(timeLeft)}</span>\n              </div>\n            </div>\n\n            {/* Progress Bar */}\n            <div className=\"mt-3 sm:mt-4\">\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div className=\"bg-gray-300 h-2 rounded-full w-1/4 animate-pulse\" />\n              </div>\n              <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                <span>Loading...</span>\n                <span>Please wait</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Loading Content */}\n        <div className=\"flex-1 flex items-center justify-center p-4\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 sm:h-16 sm:w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n            <h2 className=\"text-lg sm:text-xl font-bold text-gray-900 mb-2\">Loading Quiz</h2>\n            <p className=\"text-sm sm:text-base text-gray-600\">Please wait while we prepare your questions...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  // Fallback question data if question exists but questionData is missing\n  const safeQuestionData = questionData || {\n    text: question?.name || question?.question || \"Question loading...\",\n    type: question?.type || 'mcq',\n    options: question?.options || {},\n    imageUrl: question?.imageUrl || question?.image\n  };\n\n  return (\n    <div className=\"h-screen flex flex-col bg-gradient-to-br from-blue-50 to-indigo-100 quiz-container\">\n      {/* Header with Timer and Quiz Info */}\n      <div className=\"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50 flex-shrink-0\">\n        <div className=\"max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\">\n          <div className=\"flex items-center justify-between gap-2 sm:gap-4\">\n            {/* Quiz Title and Question Counter */}\n            <div className=\"flex-1 min-w-0\">\n              <h1 className=\"text-base sm:text-lg lg:text-xl font-bold text-gray-900 truncate\">\n                {examTitle}\n              </h1>\n              <p className=\"text-xs sm:text-sm text-gray-600 mt-1\">\n                Question {questionIndex + 1} of {totalQuestions}\n              </p>\n            </div>\n\n            {/* Timer */}\n            <div className={`flex items-center gap-1 sm:gap-2 px-2 sm:px-4 py-2 rounded-full font-bold text-xs sm:text-sm lg:text-base transition-all ${\n              isTimeWarning\n                ? 'bg-red-100 text-red-700 animate-pulse'\n                : 'bg-blue-100 text-blue-700'\n            }`}>\n              <TbClock className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              <span>{formatTime(timeLeft)}</span>\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mt-3 sm:mt-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full progress-bar\"\n                initial={{ width: 0 }}\n                animate={{ width: `${((questionIndex + 1) / totalQuestions) * 100}%` }}\n                transition={{ duration: 0.5, ease: \"easeOut\" }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n              <span>Progress</span>\n              <span>{Math.round(((questionIndex + 1) / totalQuestions) * 100)}%</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content - Scrollable with proper height */}\n      <div className=\"flex-1 overflow-y-auto quiz-content\" style={{ height: 'calc(100vh - 140px)' }}>\n        <div className=\"max-w-4xl tablet-layout desktop-layout mx-auto w-full px-3 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8\">\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={questionIndex}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              transition={{ duration: 0.3 }}\n              className=\"bg-white rounded-xl sm:rounded-2xl shadow-xl border border-gray-200 overflow-hidden\"\n            >\n              {/* Question Header */}\n              <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 px-4 sm:px-6 py-3 sm:py-4\">\n                <div className=\"flex items-center justify-between gap-2\">\n                  <div className={`px-2 sm:px-3 py-1 rounded-full text-xs font-semibold ${\n                    safeQuestionData.type === 'mcq'\n                      ? 'bg-blue-100 text-blue-800'\n                      : safeQuestionData.type === 'fill'\n                      ? 'bg-green-100 text-green-800'\n                      : 'bg-purple-100 text-purple-800'\n                  }`}>\n                    {safeQuestionData.type === 'mcq' ? 'Multiple Choice' :\n                     safeQuestionData.type === 'fill' ? 'Fill in the Blank' :\n                     'Image Question'}\n                  </div>\n                  {isAnswered && (\n                    <div className=\"flex items-center gap-1 text-white text-xs sm:text-sm font-medium\">\n                      <TbCheck className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n                      <span className=\"hidden sm:inline\">Answered</span>\n                    </div>\n                  )}\n                </div>\n              </div>\n\n              {/* Question Content - Scrollable */}\n              <div className=\"p-4 sm:p-6 lg:p-8 max-h-[calc(100vh-300px)] overflow-y-auto\">\n                {/* Question Text */}\n                <div className=\"mb-6 sm:mb-8\">\n                  <h2 className=\"quiz-question-text text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 mb-4 leading-relaxed\">\n                    {safeQuestionData.text}\n                  </h2>\n\n                  {/* Image for image-based questions */}\n                  {safeQuestionData.imageUrl && (\n                    <div className=\"mb-6\">\n                      <div className=\"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200\">\n                        <img\n                          src={safeQuestionData.imageUrl}\n                          alt=\"Question diagram\"\n                          className=\"quiz-image w-full max-w-full sm:max-w-lg lg:max-w-2xl mx-auto h-auto object-contain max-h-64 sm:max-h-80 lg:max-h-96\"\n                          style={{ display: 'block' }}\n                          onLoad={(e) => {\n                            e.target.style.display = 'block';\n                            const fallback = e.target.parentNode.querySelector('.image-fallback');\n                            if (fallback) fallback.style.display = 'none';\n                          }}\n                          onError={(e) => {\n                            e.target.style.display = 'none';\n                            const fallback = e.target.parentNode.querySelector('.image-fallback');\n                            if (fallback) fallback.style.display = 'flex';\n                          }}\n                        />\n                        <div className=\"image-fallback hidden items-center justify-center h-32 sm:h-48 text-gray-500\">\n                          <div className=\"text-center\">\n                            <TbPhoto className=\"w-8 h-8 sm:w-12 sm:h-12 mx-auto mb-2\" />\n                            <p className=\"text-sm sm:text-base\">Image not available</p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                {/* Answer Options */}\n                <div className=\"space-y-3 sm:space-y-4\">\n                  {safeQuestionData.type === 'mcq' && safeQuestionData.options && Object.keys(safeQuestionData.options).length > 0 ? (\n                    // Multiple Choice Questions\n                    Object.entries(safeQuestionData.options).map(([key, value]) => {\n                      const isSelected = currentAnswer === key;\n                      return (\n                        <motion.button\n                          key={key}\n                          onClick={() => handleAnswerChange(key)}\n                          className={`quiz-option-button quiz-button w-full p-3 sm:p-4 rounded-lg sm:rounded-xl border-2 text-left transition-all duration-200 ${\n                            isSelected\n                              ? 'border-blue-500 bg-blue-50 shadow-md'\n                              : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'\n                          }`}\n                          whileHover={{ scale: 1.01 }}\n                          whileTap={{ scale: 0.99 }}\n                        >\n                          <div className=\"flex items-start gap-3 sm:gap-4\">\n                            <div className={`w-7 h-7 sm:w-8 sm:h-8 rounded-full border-2 flex items-center justify-center font-bold text-sm flex-shrink-0 mt-0.5 ${\n                              isSelected\n                                ? 'border-blue-500 bg-blue-500 text-white'\n                                : 'border-gray-300 text-gray-600'\n                            }`}>\n                              {isSelected ? <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5\" /> : key}\n                            </div>\n                            <span className=\"quiz-option-text text-gray-900 font-medium text-sm sm:text-base leading-relaxed break-words\">{value}</span>\n                          </div>\n                        </motion.button>\n                      );\n                    })\n                  ) : (\n                    // Fill-in-the-Blank Questions\n                    <div className=\"space-y-4\">\n                      <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg sm:rounded-xl p-4 sm:p-6 border-2 border-green-200\">\n                        <div className=\"flex items-center gap-2 mb-4\">\n                          <div className=\"w-7 h-7 sm:w-8 sm:h-8 bg-green-500 rounded-full flex items-center justify-center\">\n                            <TbEdit className=\"w-4 h-4 sm:w-5 sm:h-5 text-white\" />\n                          </div>\n                          <label className=\"text-sm font-semibold text-gray-700\">\n                            Your Answer:\n                          </label>\n                        </div>\n                        <textarea\n                          value={currentAnswer}\n                          onChange={(e) => handleAnswerChange(e.target.value)}\n                          placeholder=\"Type your answer here...\"\n                          className=\"w-full p-3 sm:p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all text-sm sm:text-base\"\n                          rows=\"4\"\n                        />\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </motion.div>\n          </AnimatePresence>\n        </div>\n      </div>\n\n      {/* Navigation Footer - Fixed at bottom */}\n      <div className=\"quiz-footer bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50 flex-shrink-0\">\n        <div className=\"max-w-4xl tablet-layout desktop-layout mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4 mobile-nav\">\n          <div className=\"flex items-center justify-between gap-2 sm:gap-4\">\n            {/* Previous Button */}\n            <button\n              onClick={handlePrevious}\n              disabled={questionIndex === 0}\n              className={`quiz-nav-button quiz-button mobile-nav-button flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg sm:rounded-xl font-semibold transition-all text-sm sm:text-base ${\n                questionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'\n              }`}\n            >\n              <TbArrowLeft className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              <span className=\"hidden sm:inline\">Previous</span>\n              <span className=\"sm:hidden\">Prev</span>\n            </button>\n\n            {/* Question Navigation Dots */}\n            <div className=\"flex items-center gap-1 sm:gap-2 overflow-x-auto max-w-[200px] sm:max-w-xs lg:max-w-md scrollbar-hide\">\n              {Array.from({ length: totalQuestions }, (_, index) => {\n                const isAnswered = selectedOptions[index] !== undefined && selectedOptions[index] !== '';\n                const isCurrent = index === questionIndex;\n\n                return (\n                  <div\n                    key={index}\n                    className={`quiz-nav-button w-7 h-7 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all flex-shrink-0 ${\n                      isCurrent\n                        ? 'bg-blue-600 text-white scale-110'\n                        : isAnswered\n                        ? 'bg-green-500 text-white'\n                        : 'bg-gray-200 text-gray-600'\n                    }`}\n                  >\n                    {isAnswered && !isCurrent ? (\n                      <TbCheck className=\"w-3 h-3 sm:w-4 sm:h-4\" />\n                    ) : (\n                      index + 1\n                    )}\n                  </div>\n                );\n              })}\n            </div>\n\n            {/* Next/Submit Button */}\n            <button\n              onClick={handleNext}\n              className=\"quiz-nav-button quiz-button mobile-nav-button flex items-center gap-1 sm:gap-2 px-3 sm:px-6 py-2 sm:py-3 rounded-lg sm:rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md text-sm sm:text-base\"\n            >\n              <span className=\"hidden sm:inline\">\n                {questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'}\n              </span>\n              <span className=\"sm:hidden\">\n                {questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'}\n              </span>\n              {questionIndex === totalQuestions - 1 ? (\n                <TbCheck className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              ) : (\n                <TbArrowRight className=\"w-4 h-4 sm:w-5 sm:h-5\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NewQuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,aAAa,EACbC,QAAQ,QACH,gBAAgB;AACvB,OAAO,uBAAuB;;AAE9B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAIC,QAAQ,IAAK;EACxC,IAAI,CAACA,QAAQ,EAAE,OAAO,IAAI;EAE1B,MAAMC,YAAY,GAAGD,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACA,QAAQ,IAAIA,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACI,KAAK,IAAI,EAAE;EAChG,MAAMC,YAAY,GAAGL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACO,UAAU,IAAIP,QAAQ,CAACK,YAAY,IAAI,KAAK;EAC3F,MAAMG,eAAe,GAAGR,QAAQ,CAACS,OAAO,IAAIT,QAAQ,CAACU,OAAO,IAAIV,QAAQ,CAACW,OAAO,IAAI,CAAC,CAAC;EACtF,MAAMC,aAAa,GAAGZ,QAAQ,CAACY,aAAa,IAAIZ,QAAQ,CAACa,aAAa,IAAI,EAAE;EAC5E,MAAMC,QAAQ,GAAGd,QAAQ,CAACc,QAAQ,IAAId,QAAQ,CAACe,KAAK,IAAI,EAAE;;EAE1D;EACA,IAAIC,cAAc,GAAG,KAAK;EAC1B,MAAMC,UAAU,GAAGC,MAAM,CAACb,YAAY,CAAC,CAACc,WAAW,CAAC,CAAC;EAErD,IAAIF,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,IAAIH,UAAU,KAAK,KAAK,IAAIA,UAAU,CAACG,QAAQ,CAAC,UAAU,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,EAAE;IAC7HJ,cAAc,GAAG,KAAK;EACxB,CAAC,MAAM,IAAIC,UAAU,CAACG,QAAQ,CAAC,MAAM,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,UAAU,KAAK,MAAM,IAAIA,UAAU,CAACG,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC9HJ,cAAc,GAAG,MAAM;EACzB,CAAC,MAAM,IAAIC,UAAU,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,SAAS,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,SAAS,CAAC,EAAE;IAC3GJ,cAAc,GAAG,OAAO;EAC1B;;EAEA;EACA,IAAIA,cAAc,KAAK,KAAK,KAAK,CAACR,eAAe,IAAIa,MAAM,CAACC,IAAI,CAACd,eAAe,CAAC,CAACe,MAAM,KAAK,CAAC,CAAC,EAAE;IAC/FP,cAAc,GAAG,MAAM;EACzB;EAEA,OAAO;IACLb,IAAI,EAAEF,YAAY;IAClBK,IAAI,EAAEU,cAAc;IACpBP,OAAO,EAAED,eAAe;IACxBI,aAAa;IACbE,QAAQ;IACRU,gBAAgB,EAAExB;EACpB,CAAC;AACH,CAAC;;AAED;AACA,MAAMyB,UAAU,GAAIC,OAAO,IAAK;EAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;EACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;EACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;AACrE,CAAC;AAED,MAAMC,eAAe,GAAGA,CAAC;EACvBjC,QAAQ;EACRkC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,eAAe,GAAG,CAAC,CAAC;EACpBC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG,KAAK;EACrBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhE,QAAQ,CAACqD,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMmE,YAAY,GAAGlD,QAAQ,GAAGD,mBAAmB,CAACC,QAAQ,CAAC,GAAG,IAAI;;EAEpE;EACAhB,SAAS,CAAC,MAAM;IACdmE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvClB,aAAa;MACbC,cAAc;MACdkB,WAAW,EAAE,CAAC,CAACrD,QAAQ;MACvBsD,eAAe,EAAE,CAAC,CAACJ,YAAY;MAC/B7C,YAAY,EAAE,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,IAAI,MAAIN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,UAAU;MACpDN,YAAY,EAAE,CAAAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,IAAI,MAAIF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEA,QAAQ;MAClDuC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvC,QAAQ,EAAEkD,YAAY,EAAEhB,aAAa,EAAEC,cAAc,EAAEI,QAAQ,CAAC,CAAC;;EAErE;EACAvD,SAAS,CAAC,MAAM;IACd+D,gBAAgB,CAACX,cAAc,IAAI,EAAE,CAAC;IACtCa,aAAa,CAAC,CAAC,CAACb,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;;EAEnC;EACA,MAAMqB,kBAAkB,GAAGtE,WAAW,CAAEuE,MAAM,IAAK;IACjDT,gBAAgB,CAACS,MAAM,CAAC;IACxBP,aAAa,CAAC,CAAC,CAACO,MAAM,CAAC;IACvBlB,cAAc,CAACkB,MAAM,CAAC;EACxB,CAAC,EAAE,CAAClB,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMmB,UAAU,GAAGxE,WAAW,CAAC,MAAM;IACnC,IAAIiD,aAAa,KAAKC,cAAc,GAAG,CAAC,EAAE;MACxC;MACA,IAAIS,QAAQ,EAAE;QACZA,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,MAAM;MACLJ,MAAM,CAAC,CAAC;IACV;EACF,CAAC,EAAE,CAACN,aAAa,EAAEC,cAAc,EAAEK,MAAM,EAAEI,QAAQ,CAAC,CAAC;EAErD,MAAMc,cAAc,GAAGzE,WAAW,CAAC,MAAM;IACvC,IAAIiD,aAAa,GAAG,CAAC,EAAE;MACrBO,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACP,aAAa,EAAEO,UAAU,CAAC,CAAC;;EAE/B;EACA,IAAI,CAACzC,QAAQ,IAAI,CAACkD,YAAY,EAAE;IAC9B,oBACEpD,OAAA;MAAK6D,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBAExE9D,OAAA;QAAK6D,SAAS,EAAC,iEAAiE;QAAAC,QAAA,eAC9E9D,OAAA;UAAK6D,SAAS,EAAC,qDAAqD;UAAAC,QAAA,gBAClE9D,OAAA;YAAK6D,SAAS,EAAC,kDAAkD;YAAAC,QAAA,gBAE/D9D,OAAA;cAAK6D,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7B9D,OAAA;gBAAI6D,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,EAC7ElB;cAAS;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACLlE,OAAA;gBAAG6D,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAErD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGNlE,OAAA;cAAK6D,SAAS,EAAG,4HACfhB,aAAa,GACT,uCAAuC,GACvC,2BACL,EAAE;cAAAiB,QAAA,gBACD9D,OAAA,CAACV,OAAO;gBAACuE,SAAS,EAAC;cAAuB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7ClE,OAAA;gBAAA8D,QAAA,EAAOnC,UAAU,CAACc,QAAQ;cAAC;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlE,OAAA;YAAK6D,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B9D,OAAA;cAAK6D,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eAClD9D,OAAA;gBAAK6D,SAAS,EAAC;cAAkD;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjE,CAAC,eACNlE,OAAA;cAAK6D,SAAS,EAAC,iDAAiD;cAAAC,QAAA,gBAC9D9D,OAAA;gBAAA8D,QAAA,EAAM;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvBlE,OAAA;gBAAA8D,QAAA,EAAM;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlE,OAAA;QAAK6D,SAAS,EAAC,6CAA6C;QAAAC,QAAA,eAC1D9D,OAAA;UAAK6D,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9D,OAAA;YAAK6D,SAAS,EAAC;UAA6F;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnHlE,OAAA;YAAI6D,SAAS,EAAC,iDAAiD;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjFlE,OAAA;YAAG6D,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAA8C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMC,gBAAgB,GAAGf,YAAY,IAAI;IACvC/C,IAAI,EAAE,CAAAH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,IAAI,MAAIF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEA,QAAQ,KAAI,qBAAqB;IACnEM,IAAI,EAAE,CAAAN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,IAAI,KAAI,KAAK;IAC7BG,OAAO,EAAE,CAAAT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,OAAO,KAAI,CAAC,CAAC;IAChCK,QAAQ,EAAE,CAAAd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEc,QAAQ,MAAId,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEe,KAAK;EACjD,CAAC;EAED,oBACEjB,OAAA;IAAK6D,SAAS,EAAC,oFAAoF;IAAAC,QAAA,gBAEjG9D,OAAA;MAAK6D,SAAS,EAAC,+EAA+E;MAAAC,QAAA,eAC5F9D,OAAA;QAAK6D,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClE9D,OAAA;UAAK6D,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAE/D9D,OAAA;YAAK6D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B9D,OAAA;cAAI6D,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC7ElB;YAAS;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACLlE,OAAA;cAAG6D,SAAS,EAAC,uCAAuC;cAAAC,QAAA,GAAC,WAC1C,EAAC1B,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNlE,OAAA;YAAK6D,SAAS,EAAG,4HACfhB,aAAa,GACT,uCAAuC,GACvC,2BACL,EAAE;YAAAiB,QAAA,gBACD9D,OAAA,CAACV,OAAO;cAACuE,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7ClE,OAAA;cAAA8D,QAAA,EAAOnC,UAAU,CAACc,QAAQ;YAAC;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlE,OAAA;UAAK6D,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B9D,OAAA;YAAK6D,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClD9D,OAAA,CAACZ,MAAM,CAACgF,GAAG;cACTP,SAAS,EAAC,4EAA4E;cACtFQ,OAAO,EAAE;gBAAEC,KAAK,EAAE;cAAE,CAAE;cACtBC,OAAO,EAAE;gBAAED,KAAK,EAAG,GAAG,CAAClC,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAI;cAAG,CAAE;cACvEmC,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,IAAI,EAAE;cAAU;YAAE;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlE,OAAA;YAAK6D,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9D9D,OAAA;cAAA8D,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrBlE,OAAA;cAAA8D,QAAA,GAAOhC,IAAI,CAAC6C,KAAK,CAAE,CAACvC,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG,CAAC,EAAC,GAAC;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlE,OAAA;MAAK6D,SAAS,EAAC,qCAAqC;MAACe,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAsB,CAAE;MAAAf,QAAA,eAC5F9D,OAAA;QAAK6D,SAAS,EAAC,iGAAiG;QAAAC,QAAA,eAC9G9D,OAAA,CAACX,eAAe;UAACyF,IAAI,EAAC,MAAM;UAAAhB,QAAA,eAC1B9D,OAAA,CAACZ,MAAM,CAACgF,GAAG;YAETC,OAAO,EAAE;cAAEU,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BT,OAAO,EAAE;cAAEQ,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BC,IAAI,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;YAAG,CAAE;YAC7BR,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAC9BZ,SAAS,EAAC,qFAAqF;YAAAC,QAAA,gBAG/F9D,OAAA;cAAK6D,SAAS,EAAC,wEAAwE;cAAAC,QAAA,eACrF9D,OAAA;gBAAK6D,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,gBACtD9D,OAAA;kBAAK6D,SAAS,EAAG,wDACfM,gBAAgB,CAAC3D,IAAI,KAAK,KAAK,GAC3B,2BAA2B,GAC3B2D,gBAAgB,CAAC3D,IAAI,KAAK,MAAM,GAChC,6BAA6B,GAC7B,+BACL,EAAE;kBAAAsD,QAAA,EACAK,gBAAgB,CAAC3D,IAAI,KAAK,KAAK,GAAG,iBAAiB,GACnD2D,gBAAgB,CAAC3D,IAAI,KAAK,MAAM,GAAG,mBAAmB,GACtD;gBAAgB;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,EACLhB,UAAU,iBACTlD,OAAA;kBAAK6D,SAAS,EAAC,mEAAmE;kBAAAC,QAAA,gBAChF9D,OAAA,CAACP,OAAO;oBAACoE,SAAS,EAAC;kBAAuB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC7ClE,OAAA;oBAAM6D,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/C,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNlE,OAAA;cAAK6D,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBAE1E9D,OAAA;gBAAK6D,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3B9D,OAAA;kBAAI6D,SAAS,EAAC,oGAAoG;kBAAAC,QAAA,EAC/GK,gBAAgB,CAAC9D;gBAAI;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,EAGJC,gBAAgB,CAACnD,QAAQ,iBACxBhB,OAAA;kBAAK6D,SAAS,EAAC,MAAM;kBAAAC,QAAA,eACnB9D,OAAA;oBAAK6D,SAAS,EAAC,uEAAuE;oBAAAC,QAAA,gBACpF9D,OAAA;sBACEkF,GAAG,EAAEf,gBAAgB,CAACnD,QAAS;sBAC/BmE,GAAG,EAAC,kBAAkB;sBACtBtB,SAAS,EAAC,sHAAsH;sBAChIe,KAAK,EAAE;wBAAEQ,OAAO,EAAE;sBAAQ,CAAE;sBAC5BC,MAAM,EAAGC,CAAC,IAAK;wBACbA,CAAC,CAACC,MAAM,CAACX,KAAK,CAACQ,OAAO,GAAG,OAAO;wBAChC,MAAMI,QAAQ,GAAGF,CAAC,CAACC,MAAM,CAACE,UAAU,CAACC,aAAa,CAAC,iBAAiB,CAAC;wBACrE,IAAIF,QAAQ,EAAEA,QAAQ,CAACZ,KAAK,CAACQ,OAAO,GAAG,MAAM;sBAC/C,CAAE;sBACFO,OAAO,EAAGL,CAAC,IAAK;wBACdA,CAAC,CAACC,MAAM,CAACX,KAAK,CAACQ,OAAO,GAAG,MAAM;wBAC/B,MAAMI,QAAQ,GAAGF,CAAC,CAACC,MAAM,CAACE,UAAU,CAACC,aAAa,CAAC,iBAAiB,CAAC;wBACrE,IAAIF,QAAQ,EAAEA,QAAQ,CAACZ,KAAK,CAACQ,OAAO,GAAG,MAAM;sBAC/C;oBAAE;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eACFlE,OAAA;sBAAK6D,SAAS,EAAC,8EAA8E;sBAAAC,QAAA,eAC3F9D,OAAA;wBAAK6D,SAAS,EAAC,aAAa;wBAAAC,QAAA,gBAC1B9D,OAAA,CAACL,OAAO;0BAACkE,SAAS,EAAC;wBAAsC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC5DlE,OAAA;0BAAG6D,SAAS,EAAC,sBAAsB;0BAAAC,QAAA,EAAC;wBAAmB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNlE,OAAA;gBAAK6D,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACpCK,gBAAgB,CAAC3D,IAAI,KAAK,KAAK,IAAI2D,gBAAgB,CAACxD,OAAO,IAAIY,MAAM,CAACC,IAAI,CAAC2C,gBAAgB,CAACxD,OAAO,CAAC,CAACc,MAAM,GAAG,CAAC;gBAC9G;gBACAF,MAAM,CAACqE,OAAO,CAACzB,gBAAgB,CAACxD,OAAO,CAAC,CAACkF,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;kBAC7D,MAAMC,UAAU,GAAGhD,aAAa,KAAK8C,GAAG;kBACxC,oBACE9F,OAAA,CAACZ,MAAM,CAAC6G,MAAM;oBAEZC,OAAO,EAAEA,CAAA,KAAMzC,kBAAkB,CAACqC,GAAG,CAAE;oBACvCjC,SAAS,EAAG,4HACVmC,UAAU,GACN,sCAAsC,GACtC,iEACL,EAAE;oBACHG,UAAU,EAAE;sBAAEC,KAAK,EAAE;oBAAK,CAAE;oBAC5BC,QAAQ,EAAE;sBAAED,KAAK,EAAE;oBAAK,CAAE;oBAAAtC,QAAA,eAE1B9D,OAAA;sBAAK6D,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,gBAC9C9D,OAAA;wBAAK6D,SAAS,EAAG,uHACfmC,UAAU,GACN,wCAAwC,GACxC,+BACL,EAAE;wBAAAlC,QAAA,EACAkC,UAAU,gBAAGhG,OAAA,CAACP,OAAO;0BAACoE,SAAS,EAAC;wBAAuB;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,GAAG4B;sBAAG;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9D,CAAC,eACNlE,OAAA;wBAAM6D,SAAS,EAAC,6FAA6F;wBAAAC,QAAA,EAAEiC;sBAAK;wBAAAhC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzH;kBAAC,GAnBD4B,GAAG;oBAAA/B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAoBK,CAAC;gBAEpB,CAAC,CAAC;gBAAA;gBAEF;gBACAlE,OAAA;kBAAK6D,SAAS,EAAC,WAAW;kBAAAC,QAAA,eACxB9D,OAAA;oBAAK6D,SAAS,EAAC,4GAA4G;oBAAAC,QAAA,gBACzH9D,OAAA;sBAAK6D,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3C9D,OAAA;wBAAK6D,SAAS,EAAC,kFAAkF;wBAAAC,QAAA,eAC/F9D,OAAA,CAACJ,MAAM;0BAACiE,SAAS,EAAC;wBAAkC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpD,CAAC,eACNlE,OAAA;wBAAO6D,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,EAAC;sBAEvD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNlE,OAAA;sBACE+F,KAAK,EAAE/C,aAAc;sBACrBsD,QAAQ,EAAGhB,CAAC,IAAK7B,kBAAkB,CAAC6B,CAAC,CAACC,MAAM,CAACQ,KAAK,CAAE;sBACpDQ,WAAW,EAAC,0BAA0B;sBACtC1C,SAAS,EAAC,8KAA8K;sBACxL2C,IAAI,EAAC;oBAAG;sBAAAzC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA3HD9B,aAAa;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA4HR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlE,OAAA;MAAK6D,SAAS,EAAC,4GAA4G;MAAAC,QAAA,eACzH9D,OAAA;QAAK6D,SAAS,EAAC,6FAA6F;QAAAC,QAAA,eAC1G9D,OAAA;UAAK6D,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAE/D9D,OAAA;YACEkG,OAAO,EAAEtC,cAAe;YACxB6C,QAAQ,EAAErE,aAAa,KAAK,CAAE;YAC9ByB,SAAS,EAAG,uLACVzB,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,6DACL,EAAE;YAAA0B,QAAA,gBAEH9D,OAAA,CAACT,WAAW;cAACsE,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjDlE,OAAA;cAAM6D,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDlE,OAAA;cAAM6D,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC,eAGTlE,OAAA;YAAK6D,SAAS,EAAC,uGAAuG;YAAAC,QAAA,EACnH4C,KAAK,CAACC,IAAI,CAAC;cAAElF,MAAM,EAAEY;YAAe,CAAC,EAAE,CAACuE,CAAC,EAAEC,KAAK,KAAK;cACpD,MAAM3D,UAAU,GAAGX,eAAe,CAACsE,KAAK,CAAC,KAAKC,SAAS,IAAIvE,eAAe,CAACsE,KAAK,CAAC,KAAK,EAAE;cACxF,MAAME,SAAS,GAAGF,KAAK,KAAKzE,aAAa;cAEzC,oBACEpC,OAAA;gBAEE6D,SAAS,EAAG,sIACVkD,SAAS,GACL,kCAAkC,GAClC7D,UAAU,GACV,yBAAyB,GACzB,2BACL,EAAE;gBAAAY,QAAA,EAEFZ,UAAU,IAAI,CAAC6D,SAAS,gBACvB/G,OAAA,CAACP,OAAO;kBAACoE,SAAS,EAAC;gBAAuB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAE7C2C,KAAK,GAAG;cACT,GAbIA,KAAK;gBAAA9C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcP,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNlE,OAAA;YACEkG,OAAO,EAAEvC,UAAW;YACpBE,SAAS,EAAC,8OAA8O;YAAAC,QAAA,gBAExP9D,OAAA;cAAM6D,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC/B1B,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,QAAQ,GAAG;YAAM;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,eACPlE,OAAA;cAAM6D,SAAS,EAAC,WAAW;cAAAC,QAAA,EACxB1B,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,QAAQ,GAAG;YAAM;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,EACN9B,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCrC,OAAA,CAACP,OAAO;cAACoE,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE7ClE,OAAA,CAACR,YAAY;cAACqE,SAAS,EAAC;YAAuB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAClD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnB,EAAA,CApXIZ,eAAe;AAAA6E,EAAA,GAAf7E,eAAe;AAsXrB,eAAeA,eAAe;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}