{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck, TbX, TbPhoto, TbEdit, TbFlag } from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport './responsive.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const {\n    user\n  } = useSelector(state => state.user);\n\n  // Quiz state\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes default\n  const [startTime, setStartTime] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        const response = await getExamById({\n          examId: id\n        });\n        if (response.success) {\n          setExamData(response.data);\n          setQuestions(response.data.questions || []);\n          setTimeLeft((response.data.duration || 30) * 60);\n          setStartTime(new Date());\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        message.error('Failed to load quiz');\n        navigate('/user/quiz');\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (id) {\n      loadQuizData();\n    }\n  }, [id, navigate]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Format time display\n  const formatTime = seconds => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = answer => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestionIndex]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = async () => {\n    try {\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n\n      // Calculate results\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n        return {\n          question: question._id,\n          selectedAnswer: userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n      const percentage = Math.round(correctAnswers / questions.length * 100);\n\n      // Submit to backend\n      const reportData = {\n        exam: id,\n        user: user._id,\n        correctAnswers: resultDetails.filter(r => r.isCorrect),\n        wrongAnswers: resultDetails.filter(r => !r.isCorrect),\n        percentage,\n        timeTaken\n      };\n      await addReport(reportData);\n\n      // Navigate to results\n      navigate(`/quiz/${id}/result`, {\n        state: {\n          percentage,\n          correctAnswers,\n          totalQuestions: questions.length,\n          timeTaken,\n          resultDetails\n        }\n      });\n    } catch (error) {\n      message.error('Failed to submit quiz');\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Loading quiz...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this);\n  }\n  if (!examData || questions.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(TbX, {\n          className: \"w-16 h-16 text-red-500 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900 mb-2\",\n          children: \"Quiz Not Available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 mb-4\",\n          children: \"This quiz could not be loaded or has no questions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => navigate('/user/quiz'),\n          className: \"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Back to Quizzes\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this);\n  }\n  const currentQuestion = questions[currentQuestionIndex];\n  const progress = (currentQuestionIndex + 1) / questions.length * 100;\n  const isTimeWarning = timeLeft <= 300; // 5 minutes warning\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-gray-900 truncate\",\n              children: examData.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mt-1\",\n              children: [\"Question \", currentQuestionIndex + 1, \" of \", questions.length]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-2 px-4 py-2 rounded-full font-bold text-base transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 animate-pulse' : 'bg-blue-100 text-blue-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full\",\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${progress}%`\n              },\n              transition: {\n                duration: 0.5\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-xs text-gray-500 mt-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [Math.round(progress), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 py-6\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          exit: {\n            opacity: 0,\n            x: -20\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `px-3 py-1 rounded-full text-xs font-semibold ${currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'}`,\n                children: currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options' ? 'Multiple Choice' : 'Fill in the Blank'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), answers[currentQuestionIndex] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-1 text-white text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 21\n                }, this), \"Answered\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 max-h-[60vh] overflow-y-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl font-semibold text-gray-900 mb-4 leading-relaxed\",\n                children: currentQuestion.name || currentQuestion.question\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 17\n              }, this), currentQuestion.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: currentQuestion.imageUrl,\n                    alt: \"Question diagram\",\n                    className: \"w-full max-w-2xl mx-auto h-auto object-contain max-h-80\",\n                    onError: e => {\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hidden items-center justify-center h-48 text-gray-500\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(TbPhoto, {\n                        className: \"w-12 h-12 mx-auto mb-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 287,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"Image not available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 288,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 286,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 275,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: (currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options') && currentQuestion.options ?\n              // Multiple Choice Questions\n              Object.entries(currentQuestion.options).map(([key, value]) => {\n                const isSelected = answers[currentQuestionIndex] === key;\n                return /*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: () => handleAnswerSelect(key),\n                  className: `w-full p-4 rounded-xl border-2 text-left transition-all duration-200 ${isSelected ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'}`,\n                  whileHover: {\n                    scale: 1.01\n                  },\n                  whileTap: {\n                    scale: 0.99\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-600'}`,\n                      children: isSelected ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-5 h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 320,\n                        columnNumber: 43\n                      }, this) : key\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 315,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-900 font-medium\",\n                      children: value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 322,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 314,\n                    columnNumber: 25\n                  }, this)\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 23\n                }, this);\n              }) :\n              /*#__PURE__*/\n              // Fill-in-the-Blank Questions\n              _jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border-2 border-green-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(TbEdit, {\n                      className: \"w-5 h-5 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-sm font-semibold text-gray-700\",\n                    children: \"Your Answer:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 334,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: answers[currentQuestionIndex] || '',\n                  onChange: e => handleAnswerSelect(e.target.value),\n                  placeholder: \"Type your answer here...\",\n                  className: \"w-full p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all\",\n                  rows: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, currentQuestionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"sjPc4WX/o3i+A7ACKMAAP624uZg=\", false, function () {\n  return [useParams, useNavigate, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useDispatch", "useSelector", "message", "motion", "AnimatePresence", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbX", "TbPhoto", "TbEdit", "TbFlag", "getExamById", "addReport", "jsxDEV", "_jsxDEV", "QuizPlay", "_s", "id", "navigate", "user", "state", "examData", "setExamData", "questions", "setQuestions", "currentQuestionIndex", "setCurrentQuestionIndex", "answers", "setAnswers", "timeLeft", "setTimeLeft", "startTime", "setStartTime", "loading", "setLoading", "loadQuizData", "response", "examId", "success", "data", "duration", "Date", "error", "handleSubmitQuiz", "timer", "setInterval", "prev", "clearInterval", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "handleAnswerSelect", "answer", "goToNext", "length", "goToPrevious", "endTime", "timeTaken", "correctAnswers", "resultDetails", "map", "question", "index", "userAnswer", "isCorrect", "<PERSON><PERSON><PERSON><PERSON>", "_id", "<PERSON><PERSON><PERSON><PERSON>", "percentage", "round", "reportData", "exam", "filter", "r", "wrongAnswers", "totalQuestions", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "currentQuestion", "progress", "isTimeWarning", "name", "div", "initial", "width", "animate", "transition", "mode", "opacity", "x", "exit", "type", "answerType", "imageUrl", "src", "alt", "onError", "e", "target", "style", "display", "nextS<PERSON>ling", "options", "Object", "entries", "key", "value", "isSelected", "button", "whileHover", "scale", "whileTap", "onChange", "placeholder", "rows", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Tb<PERSON>lock, \n  TbArrowLeft, \n  TbArrowRight, \n  TbCheck, \n  TbX,\n  TbPhoto,\n  TbEdit,\n  TbFlag\n} from 'react-icons/tb';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport './responsive.css';\n\nconst QuizPlay = () => {\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const { user } = useSelector((state) => state.user);\n\n  // Quiz state\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);\n  const [answers, setAnswers] = useState({});\n  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes default\n  const [startTime, setStartTime] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  // Load quiz data\n  useEffect(() => {\n    const loadQuizData = async () => {\n      try {\n        setLoading(true);\n        const response = await getExamById({ examId: id });\n        \n        if (response.success) {\n          setExamData(response.data);\n          setQuestions(response.data.questions || []);\n          setTimeLeft((response.data.duration || 30) * 60);\n          setStartTime(new Date());\n        } else {\n          message.error(response.message);\n          navigate('/user/quiz');\n        }\n      } catch (error) {\n        message.error('Failed to load quiz');\n        navigate('/user/quiz');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (id) {\n      loadQuizData();\n    }\n  }, [id, navigate]);\n\n  // Timer countdown\n  useEffect(() => {\n    if (timeLeft <= 0) {\n      handleSubmitQuiz();\n      return;\n    }\n\n    const timer = setInterval(() => {\n      setTimeLeft(prev => prev - 1);\n    }, 1000);\n\n    return () => clearInterval(timer);\n  }, [timeLeft]);\n\n  // Format time display\n  const formatTime = (seconds) => {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = seconds % 60;\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  };\n\n  // Handle answer selection\n  const handleAnswerSelect = (answer) => {\n    setAnswers(prev => ({\n      ...prev,\n      [currentQuestionIndex]: answer\n    }));\n  };\n\n  // Navigation functions\n  const goToNext = () => {\n    if (currentQuestionIndex < questions.length - 1) {\n      setCurrentQuestionIndex(prev => prev + 1);\n    }\n  };\n\n  const goToPrevious = () => {\n    if (currentQuestionIndex > 0) {\n      setCurrentQuestionIndex(prev => prev - 1);\n    }\n  };\n\n  // Submit quiz\n  const handleSubmitQuiz = async () => {\n    try {\n      const endTime = new Date();\n      const timeTaken = Math.floor((endTime - startTime) / 1000);\n      \n      // Calculate results\n      let correctAnswers = 0;\n      const resultDetails = questions.map((question, index) => {\n        const userAnswer = answers[index];\n        const isCorrect = userAnswer === question.correctAnswer;\n        if (isCorrect) correctAnswers++;\n        \n        return {\n          question: question._id,\n          selectedAnswer: userAnswer,\n          correctAnswer: question.correctAnswer,\n          isCorrect\n        };\n      });\n\n      const percentage = Math.round((correctAnswers / questions.length) * 100);\n      \n      // Submit to backend\n      const reportData = {\n        exam: id,\n        user: user._id,\n        correctAnswers: resultDetails.filter(r => r.isCorrect),\n        wrongAnswers: resultDetails.filter(r => !r.isCorrect),\n        percentage,\n        timeTaken\n      };\n\n      await addReport(reportData);\n      \n      // Navigate to results\n      navigate(`/quiz/${id}/result`, {\n        state: {\n          percentage,\n          correctAnswers,\n          totalQuestions: questions.length,\n          timeTaken,\n          resultDetails\n        }\n      });\n    } catch (error) {\n      message.error('Failed to submit quiz');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600 font-medium\">Loading quiz...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!examData || questions.length === 0) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <TbX className=\"w-16 h-16 text-red-500 mx-auto mb-4\" />\n          <h2 className=\"text-xl font-bold text-gray-900 mb-2\">Quiz Not Available</h2>\n          <p className=\"text-gray-600 mb-4\">This quiz could not be loaded or has no questions.</p>\n          <button\n            onClick={() => navigate('/user/quiz')}\n            className=\"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Back to Quizzes\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const currentQuestion = questions[currentQuestionIndex];\n  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;\n  const isTimeWarning = timeLeft <= 300; // 5 minutes warning\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header */}\n      <div className=\"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Quiz Info */}\n            <div className=\"flex-1\">\n              <h1 className=\"text-xl font-bold text-gray-900 truncate\">\n                {examData.name}\n              </h1>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                Question {currentQuestionIndex + 1} of {questions.length}\n              </p>\n            </div>\n\n            {/* Timer */}\n            <div className={`flex items-center gap-2 px-4 py-2 rounded-full font-bold text-base transition-all ${\n              isTimeWarning \n                ? 'bg-red-100 text-red-700 animate-pulse' \n                : 'bg-blue-100 text-blue-700'\n            }`}>\n              <TbClock className=\"w-5 h-5\" />\n              <span>{formatTime(timeLeft)}</span>\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mt-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: `${progress}%` }}\n                transition={{ duration: 0.5 }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n              <span>Progress</span>\n              <span>{Math.round(progress)}%</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"max-w-4xl mx-auto px-4 py-6\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={currentQuestionIndex}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: -20 }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden\"\n          >\n            {/* Question Header */}\n            <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className={`px-3 py-1 rounded-full text-xs font-semibold ${\n                  currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options'\n                    ? 'bg-blue-100 text-blue-800'\n                    : 'bg-green-100 text-green-800'\n                }`}>\n                  {currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options' \n                    ? 'Multiple Choice' \n                    : 'Fill in the Blank'}\n                </div>\n                {answers[currentQuestionIndex] && (\n                  <div className=\"flex items-center gap-1 text-white text-sm font-medium\">\n                    <TbCheck className=\"w-4 h-4\" />\n                    Answered\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Question Content */}\n            <div className=\"p-6 max-h-[60vh] overflow-y-auto\">\n              <div className=\"mb-6\">\n                <h2 className=\"text-xl font-semibold text-gray-900 mb-4 leading-relaxed\">\n                  {currentQuestion.name || currentQuestion.question}\n                </h2>\n                \n                {/* Image Display */}\n                {currentQuestion.imageUrl && (\n                  <div className=\"mb-6\">\n                    <div className=\"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200\">\n                      <img \n                        src={currentQuestion.imageUrl} \n                        alt=\"Question diagram\"\n                        className=\"w-full max-w-2xl mx-auto h-auto object-contain max-h-80\"\n                        onError={(e) => {\n                          e.target.style.display = 'none';\n                          e.target.nextSibling.style.display = 'flex';\n                        }}\n                      />\n                      <div className=\"hidden items-center justify-center h-48 text-gray-500\">\n                        <div className=\"text-center\">\n                          <TbPhoto className=\"w-12 h-12 mx-auto mb-2\" />\n                          <p>Image not available</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {/* Answer Options */}\n              <div className=\"space-y-4\">\n                {(currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options') && currentQuestion.options ? (\n                  // Multiple Choice Questions\n                  Object.entries(currentQuestion.options).map(([key, value]) => {\n                    const isSelected = answers[currentQuestionIndex] === key;\n                    return (\n                      <motion.button\n                        key={key}\n                        onClick={() => handleAnswerSelect(key)}\n                        className={`w-full p-4 rounded-xl border-2 text-left transition-all duration-200 ${\n                          isSelected\n                            ? 'border-blue-500 bg-blue-50 shadow-md'\n                            : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'\n                        }`}\n                        whileHover={{ scale: 1.01 }}\n                        whileTap={{ scale: 0.99 }}\n                      >\n                        <div className=\"flex items-center gap-4\">\n                          <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold ${\n                            isSelected\n                              ? 'border-blue-500 bg-blue-500 text-white'\n                              : 'border-gray-300 text-gray-600'\n                          }`}>\n                            {isSelected ? <TbCheck className=\"w-5 h-5\" /> : key}\n                          </div>\n                          <span className=\"text-gray-900 font-medium\">{value}</span>\n                        </div>\n                      </motion.button>\n                    );\n                  })\n                ) : (\n                  // Fill-in-the-Blank Questions\n                  <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border-2 border-green-200\">\n                    <div className=\"flex items-center gap-2 mb-4\">\n                      <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n                        <TbEdit className=\"w-5 h-5 text-white\" />\n                      </div>\n                      <label className=\"text-sm font-semibold text-gray-700\">\n                        Your Answer:\n                      </label>\n                    </div>\n                    <textarea\n                      value={answers[currentQuestionIndex] || ''}\n                      onChange={(e) => handleAnswerSelect(e.target.value)}\n                      placeholder=\"Type your answer here...\"\n                      className=\"w-full p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all\"\n                      rows=\"4\"\n                    />\n                  </div>\n                )}\n              </div>\n            </div>\n          </motion.div>\n        </AnimatePresence>\n      </div>\n    </div>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,MAAM,QACD,gBAAgB;AACvB,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAG,CAAC,GAAGrB,SAAS,CAAC,CAAC;EAC1B,MAAMsB,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEsB;EAAK,CAAC,GAAGpB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;;EAEnD;EACA,MAAM,CAACE,QAAQ,EAAEC,WAAW,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAAC6B,SAAS,EAAEC,YAAY,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAAC+B,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGhC,QAAQ,CAAC,CAAC,CAAC;EACnE,MAAM,CAACiC,OAAO,EAAEC,UAAU,CAAC,GAAGlC,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAChD,MAAM,CAACqC,SAAS,EAAEC,YAAY,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMwC,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACFD,UAAU,CAAC,IAAI,CAAC;QAChB,MAAME,QAAQ,GAAG,MAAMzB,WAAW,CAAC;UAAE0B,MAAM,EAAEpB;QAAG,CAAC,CAAC;QAElD,IAAImB,QAAQ,CAACE,OAAO,EAAE;UACpBhB,WAAW,CAACc,QAAQ,CAACG,IAAI,CAAC;UAC1Bf,YAAY,CAACY,QAAQ,CAACG,IAAI,CAAChB,SAAS,IAAI,EAAE,CAAC;UAC3CO,WAAW,CAAC,CAACM,QAAQ,CAACG,IAAI,CAACC,QAAQ,IAAI,EAAE,IAAI,EAAE,CAAC;UAChDR,YAAY,CAAC,IAAIS,IAAI,CAAC,CAAC,CAAC;QAC1B,CAAC,MAAM;UACLzC,OAAO,CAAC0C,KAAK,CAACN,QAAQ,CAACpC,OAAO,CAAC;UAC/BkB,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,CAAC,OAAOwB,KAAK,EAAE;QACd1C,OAAO,CAAC0C,KAAK,CAAC,qBAAqB,CAAC;QACpCxB,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,SAAS;QACRgB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIjB,EAAE,EAAE;MACNkB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAClB,EAAE,EAAEC,QAAQ,CAAC,CAAC;;EAElB;EACAvB,SAAS,CAAC,MAAM;IACd,IAAIkC,QAAQ,IAAI,CAAC,EAAE;MACjBc,gBAAgB,CAAC,CAAC;MAClB;IACF;IAEA,MAAMC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9Bf,WAAW,CAACgB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC/B,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMC,aAAa,CAACH,KAAK,CAAC;EACnC,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMmB,UAAU,GAAIC,OAAO,IAAK;IAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;IACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;EACrE,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAIC,MAAM,IAAK;IACrC7B,UAAU,CAACkB,IAAI,KAAK;MAClB,GAAGA,IAAI;MACP,CAACrB,oBAAoB,GAAGgC;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACrB,IAAIjC,oBAAoB,GAAGF,SAAS,CAACoC,MAAM,GAAG,CAAC,EAAE;MAC/CjC,uBAAuB,CAACoB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;EAED,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInC,oBAAoB,GAAG,CAAC,EAAE;MAC5BC,uBAAuB,CAACoB,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMH,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMkB,OAAO,GAAG,IAAIpB,IAAI,CAAC,CAAC;MAC1B,MAAMqB,SAAS,GAAGX,IAAI,CAACC,KAAK,CAAC,CAACS,OAAO,GAAG9B,SAAS,IAAI,IAAI,CAAC;;MAE1D;MACA,IAAIgC,cAAc,GAAG,CAAC;MACtB,MAAMC,aAAa,GAAGzC,SAAS,CAAC0C,GAAG,CAAC,CAACC,QAAQ,EAAEC,KAAK,KAAK;QACvD,MAAMC,UAAU,GAAGzC,OAAO,CAACwC,KAAK,CAAC;QACjC,MAAME,SAAS,GAAGD,UAAU,KAAKF,QAAQ,CAACI,aAAa;QACvD,IAAID,SAAS,EAAEN,cAAc,EAAE;QAE/B,OAAO;UACLG,QAAQ,EAAEA,QAAQ,CAACK,GAAG;UACtBC,cAAc,EAAEJ,UAAU;UAC1BE,aAAa,EAAEJ,QAAQ,CAACI,aAAa;UACrCD;QACF,CAAC;MACH,CAAC,CAAC;MAEF,MAAMI,UAAU,GAAGtB,IAAI,CAACuB,KAAK,CAAEX,cAAc,GAAGxC,SAAS,CAACoC,MAAM,GAAI,GAAG,CAAC;;MAExE;MACA,MAAMgB,UAAU,GAAG;QACjBC,IAAI,EAAE3D,EAAE;QACRE,IAAI,EAAEA,IAAI,CAACoD,GAAG;QACdR,cAAc,EAAEC,aAAa,CAACa,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACT,SAAS,CAAC;QACtDU,YAAY,EAAEf,aAAa,CAACa,MAAM,CAACC,CAAC,IAAI,CAACA,CAAC,CAACT,SAAS,CAAC;QACrDI,UAAU;QACVX;MACF,CAAC;MAED,MAAMlD,SAAS,CAAC+D,UAAU,CAAC;;MAE3B;MACAzD,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;QAC7BG,KAAK,EAAE;UACLqD,UAAU;UACVV,cAAc;UACdiB,cAAc,EAAEzD,SAAS,CAACoC,MAAM;UAChCG,SAAS;UACTE;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACd1C,OAAO,CAAC0C,KAAK,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC;EAED,IAAIT,OAAO,EAAE;IACX,oBACEnB,OAAA;MAAKmE,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGpE,OAAA;QAAKmE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpE,OAAA;UAAKmE,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGxE,OAAA;UAAGmE,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI,CAACjE,QAAQ,IAAIE,SAAS,CAACoC,MAAM,KAAK,CAAC,EAAE;IACvC,oBACE7C,OAAA;MAAKmE,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzGpE,OAAA;QAAKmE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpE,OAAA,CAACP,GAAG;UAAC0E,SAAS,EAAC;QAAqC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvDxE,OAAA;UAAImE,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5ExE,OAAA;UAAGmE,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAkD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxFxE,OAAA;UACEyE,OAAO,EAAEA,CAAA,KAAMrE,QAAQ,CAAC,YAAY,CAAE;UACtC+D,SAAS,EAAC,iFAAiF;UAAAC,QAAA,EAC5F;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,MAAME,eAAe,GAAGjE,SAAS,CAACE,oBAAoB,CAAC;EACvD,MAAMgE,QAAQ,GAAI,CAAChE,oBAAoB,GAAG,CAAC,IAAIF,SAAS,CAACoC,MAAM,GAAI,GAAG;EACtE,MAAM+B,aAAa,GAAG7D,QAAQ,IAAI,GAAG,CAAC,CAAC;;EAEvC,oBACEf,OAAA;IAAKmE,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExEpE,OAAA;MAAKmE,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9EpE,OAAA;QAAKmE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CpE,OAAA;UAAKmE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAEhDpE,OAAA;YAAKmE,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBpE,OAAA;cAAImE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACrD7D,QAAQ,CAACsE;YAAI;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLxE,OAAA;cAAGmE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,WAC/B,EAACzD,oBAAoB,GAAG,CAAC,EAAC,MAAI,EAACF,SAAS,CAACoC,MAAM;YAAA;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNxE,OAAA;YAAKmE,SAAS,EAAG,qFACfS,aAAa,GACT,uCAAuC,GACvC,2BACL,EAAE;YAAAR,QAAA,gBACDpE,OAAA,CAACX,OAAO;cAAC8E,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/BxE,OAAA;cAAAoE,QAAA,EAAOlC,UAAU,CAACnB,QAAQ;YAAC;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxE,OAAA;UAAKmE,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBpE,OAAA;YAAKmE,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDpE,OAAA,CAACb,MAAM,CAAC2F,GAAG;cACTX,SAAS,EAAC,+DAA+D;cACzEY,OAAO,EAAE;gBAAEC,KAAK,EAAE;cAAE,CAAE;cACtBC,OAAO,EAAE;gBAAED,KAAK,EAAG,GAAEL,QAAS;cAAG,CAAE;cACnCO,UAAU,EAAE;gBAAExD,QAAQ,EAAE;cAAI;YAAE;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNxE,OAAA;YAAKmE,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DpE,OAAA;cAAAoE,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrBxE,OAAA;cAAAoE,QAAA,GAAO/B,IAAI,CAACuB,KAAK,CAACe,QAAQ,CAAC,EAAC,GAAC;YAAA;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxE,OAAA;MAAKmE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CpE,OAAA,CAACZ,eAAe;QAAC+F,IAAI,EAAC,MAAM;QAAAf,QAAA,eAC1BpE,OAAA,CAACb,MAAM,CAAC2F,GAAG;UAETC,OAAO,EAAE;YAAEK,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BJ,OAAO,EAAE;YAAEG,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BC,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BH,UAAU,EAAE;YAAExD,QAAQ,EAAE;UAAI,CAAE;UAC9ByC,SAAS,EAAC,uEAAuE;UAAAC,QAAA,gBAGjFpE,OAAA;YAAKmE,SAAS,EAAC,wDAAwD;YAAAC,QAAA,eACrEpE,OAAA;cAAKmE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDpE,OAAA;gBAAKmE,SAAS,EAAG,gDACfO,eAAe,CAACa,IAAI,KAAK,KAAK,IAAIb,eAAe,CAACc,UAAU,KAAK,SAAS,GACtE,2BAA2B,GAC3B,6BACL,EAAE;gBAAApB,QAAA,EACAM,eAAe,CAACa,IAAI,KAAK,KAAK,IAAIb,eAAe,CAACc,UAAU,KAAK,SAAS,GACvE,iBAAiB,GACjB;cAAmB;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,EACL3D,OAAO,CAACF,oBAAoB,CAAC,iBAC5BX,OAAA;gBAAKmE,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,gBACrEpE,OAAA,CAACR,OAAO;kBAAC2E,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAEjC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxE,OAAA;YAAKmE,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CpE,OAAA;cAAKmE,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBpE,OAAA;gBAAImE,SAAS,EAAC,0DAA0D;gBAAAC,QAAA,EACrEM,eAAe,CAACG,IAAI,IAAIH,eAAe,CAACtB;cAAQ;gBAAAiB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,EAGJE,eAAe,CAACe,QAAQ,iBACvBzF,OAAA;gBAAKmE,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnBpE,OAAA;kBAAKmE,SAAS,EAAC,uEAAuE;kBAAAC,QAAA,gBACpFpE,OAAA;oBACE0F,GAAG,EAAEhB,eAAe,CAACe,QAAS;oBAC9BE,GAAG,EAAC,kBAAkB;oBACtBxB,SAAS,EAAC,yDAAyD;oBACnEyB,OAAO,EAAGC,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;sBAC/BH,CAAC,CAACC,MAAM,CAACG,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,MAAM;oBAC7C;kBAAE;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACFxE,OAAA;oBAAKmE,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,eACpEpE,OAAA;sBAAKmE,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1BpE,OAAA,CAACN,OAAO;wBAACyE,SAAS,EAAC;sBAAwB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC9CxE,OAAA;wBAAAoE,QAAA,EAAG;sBAAmB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNxE,OAAA;cAAKmE,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvB,CAACM,eAAe,CAACa,IAAI,KAAK,KAAK,IAAIb,eAAe,CAACc,UAAU,KAAK,SAAS,KAAKd,eAAe,CAACwB,OAAO;cACtG;cACAC,MAAM,CAACC,OAAO,CAAC1B,eAAe,CAACwB,OAAO,CAAC,CAAC/C,GAAG,CAAC,CAAC,CAACkD,GAAG,EAAEC,KAAK,CAAC,KAAK;gBAC5D,MAAMC,UAAU,GAAG1F,OAAO,CAACF,oBAAoB,CAAC,KAAK0F,GAAG;gBACxD,oBACErG,OAAA,CAACb,MAAM,CAACqH,MAAM;kBAEZ/B,OAAO,EAAEA,CAAA,KAAM/B,kBAAkB,CAAC2D,GAAG,CAAE;kBACvClC,SAAS,EAAG,wEACVoC,UAAU,GACN,sCAAsC,GACtC,iEACL,EAAE;kBACHE,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAAAtC,QAAA,eAE1BpE,OAAA;oBAAKmE,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtCpE,OAAA;sBAAKmE,SAAS,EAAG,4EACfoC,UAAU,GACN,wCAAwC,GACxC,+BACL,EAAE;sBAAAnC,QAAA,EACAmC,UAAU,gBAAGvG,OAAA,CAACR,OAAO;wBAAC2E,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAG6B;oBAAG;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,eACNxE,OAAA;sBAAMmE,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAEkC;oBAAK;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD;gBAAC,GAnBD6B,GAAG;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBK,CAAC;cAEpB,CAAC,CAAC;cAAA;cAEF;cACAxE,OAAA;gBAAKmE,SAAS,EAAC,uFAAuF;gBAAAC,QAAA,gBACpGpE,OAAA;kBAAKmE,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAC3CpE,OAAA;oBAAKmE,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eACjFpE,OAAA,CAACL,MAAM;sBAACwE,SAAS,EAAC;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACNxE,OAAA;oBAAOmE,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEvD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACNxE,OAAA;kBACEsG,KAAK,EAAEzF,OAAO,CAACF,oBAAoB,CAAC,IAAI,EAAG;kBAC3CiG,QAAQ,EAAGf,CAAC,IAAKnD,kBAAkB,CAACmD,CAAC,CAACC,MAAM,CAACQ,KAAK,CAAE;kBACpDO,WAAW,EAAC,0BAA0B;kBACtC1C,SAAS,EAAC,kJAAkJ;kBAC5J2C,IAAI,EAAC;gBAAG;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GA/GD7D,oBAAoB;UAAA0D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgHf;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtE,EAAA,CA9UID,QAAQ;EAAA,QACGnB,SAAS,EACPC,WAAW,EACXE,WAAW;AAAA;AAAA8H,EAAA,GAHxB9G,QAAQ;AAgVd,eAAeA,QAAQ;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}