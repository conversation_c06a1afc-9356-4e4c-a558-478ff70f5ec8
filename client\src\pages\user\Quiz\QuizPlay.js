import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { message } from 'antd';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Tb<PERSON>lock, 
  TbArrowLeft, 
  TbArrowRight, 
  TbCheck, 
  TbX,
  TbPhoto,
  TbEdit,
  TbFlag
} from 'react-icons/tb';
import { getExamById } from '../../../apicalls/exams';
import { addReport } from '../../../apicalls/reports';
import './responsive.css';

const QuizPlay = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.user);

  // Quiz state
  const [examData, setExamData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [timeLeft, setTimeLeft] = useState(1800); // 30 minutes default
  const [startTime, setStartTime] = useState(null);
  const [loading, setLoading] = useState(true);

  // Load quiz data
  useEffect(() => {
    const loadQuizData = async () => {
      try {
        setLoading(true);
        const response = await getExamById({ examId: id });
        
        if (response.success) {
          setExamData(response.data);
          setQuestions(response.data.questions || []);
          setTimeLeft((response.data.duration || 30) * 60);
          setStartTime(new Date());
        } else {
          message.error(response.message);
          navigate('/user/quiz');
        }
      } catch (error) {
        message.error('Failed to load quiz');
        navigate('/user/quiz');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      loadQuizData();
    }
  }, [id, navigate]);

  // Timer countdown
  useEffect(() => {
    if (timeLeft <= 0) {
      handleSubmitQuiz();
      return;
    }

    const timer = setInterval(() => {
      setTimeLeft(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeLeft]);

  // Format time display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Handle answer selection
  const handleAnswerSelect = (answer) => {
    setAnswers(prev => ({
      ...prev,
      [currentQuestionIndex]: answer
    }));
  };

  // Navigation functions
  const goToNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // Submit quiz
  const handleSubmitQuiz = async () => {
    try {
      const endTime = new Date();
      const timeTaken = Math.floor((endTime - startTime) / 1000);
      
      // Calculate results
      let correctAnswers = 0;
      const resultDetails = questions.map((question, index) => {
        const userAnswer = answers[index];
        const isCorrect = userAnswer === question.correctAnswer;
        if (isCorrect) correctAnswers++;
        
        return {
          question: question._id,
          selectedAnswer: userAnswer,
          correctAnswer: question.correctAnswer,
          isCorrect
        };
      });

      const percentage = Math.round((correctAnswers / questions.length) * 100);
      
      // Submit to backend
      const reportData = {
        exam: id,
        user: user._id,
        correctAnswers: resultDetails.filter(r => r.isCorrect),
        wrongAnswers: resultDetails.filter(r => !r.isCorrect),
        percentage,
        timeTaken
      };

      await addReport(reportData);
      
      // Navigate to results
      navigate(`/quiz/${id}/result`, {
        state: {
          percentage,
          correctAnswers,
          totalQuestions: questions.length,
          timeTaken,
          resultDetails
        }
      });
    } catch (error) {
      message.error('Failed to submit quiz');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading quiz...</p>
        </div>
      </div>
    );
  }

  if (!examData || questions.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <TbX className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-gray-900 mb-2">Quiz Not Available</h2>
          <p className="text-gray-600 mb-4">This quiz could not be loaded or has no questions.</p>
          <button
            onClick={() => navigate('/user/quiz')}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Quizzes
          </button>
        </div>
      </div>
    );
  }

  const currentQuestion = questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / questions.length) * 100;
  const isTimeWarning = timeLeft <= 300; // 5 minutes warning

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            {/* Quiz Info */}
            <div className="flex-1">
              <h1 className="text-xl font-bold text-gray-900 truncate">
                {examData.name}
              </h1>
              <p className="text-sm text-gray-600 mt-1">
                Question {currentQuestionIndex + 1} of {questions.length}
              </p>
            </div>

            {/* Timer */}
            <div className={`flex items-center gap-2 px-4 py-2 rounded-full font-bold text-base transition-all ${
              isTimeWarning 
                ? 'bg-red-100 text-red-700 animate-pulse' 
                : 'bg-blue-100 text-blue-700'
            }`}>
              <TbClock className="w-5 h-5" />
              <span>{formatTime(timeLeft)}</span>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <motion.div
                className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.5 }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 py-6">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentQuestionIndex}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden"
          >
            {/* Question Header */}
            <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4">
              <div className="flex items-center justify-between">
                <div className={`px-3 py-1 rounded-full text-xs font-semibold ${
                  currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options'
                    ? 'bg-blue-100 text-blue-800'
                    : 'bg-green-100 text-green-800'
                }`}>
                  {currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options' 
                    ? 'Multiple Choice' 
                    : 'Fill in the Blank'}
                </div>
                {answers[currentQuestionIndex] && (
                  <div className="flex items-center gap-1 text-white text-sm font-medium">
                    <TbCheck className="w-4 h-4" />
                    Answered
                  </div>
                )}
              </div>
            </div>

            {/* Question Content */}
            <div className="p-6 max-h-[60vh] overflow-y-auto">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4 leading-relaxed">
                  {currentQuestion.name || currentQuestion.question}
                </h2>
                
                {/* Image Display */}
                {currentQuestion.imageUrl && (
                  <div className="mb-6">
                    <div className="relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200">
                      <img 
                        src={currentQuestion.imageUrl} 
                        alt="Question diagram"
                        className="w-full max-w-2xl mx-auto h-auto object-contain max-h-80"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'flex';
                        }}
                      />
                      <div className="hidden items-center justify-center h-48 text-gray-500">
                        <div className="text-center">
                          <TbPhoto className="w-12 h-12 mx-auto mb-2" />
                          <p>Image not available</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Answer Options */}
              <div className="space-y-4">
                {(currentQuestion.type === 'mcq' || currentQuestion.answerType === 'Options') && currentQuestion.options ? (
                  // Multiple Choice Questions
                  Object.entries(currentQuestion.options).map(([key, value]) => {
                    const isSelected = answers[currentQuestionIndex] === key;
                    return (
                      <motion.button
                        key={key}
                        onClick={() => handleAnswerSelect(key)}
                        className={`w-full p-4 rounded-xl border-2 text-left transition-all duration-200 ${
                          isSelected
                            ? 'border-blue-500 bg-blue-50 shadow-md'
                            : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'
                        }`}
                        whileHover={{ scale: 1.01 }}
                        whileTap={{ scale: 0.99 }}
                      >
                        <div className="flex items-center gap-4">
                          <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold ${
                            isSelected
                              ? 'border-blue-500 bg-blue-500 text-white'
                              : 'border-gray-300 text-gray-600'
                          }`}>
                            {isSelected ? <TbCheck className="w-5 h-5" /> : key}
                          </div>
                          <span className="text-gray-900 font-medium">{value}</span>
                        </div>
                      </motion.button>
                    );
                  })
                ) : (
                  // Fill-in-the-Blank Questions
                  <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border-2 border-green-200">
                    <div className="flex items-center gap-2 mb-4">
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <TbEdit className="w-5 h-5 text-white" />
                      </div>
                      <label className="text-sm font-semibold text-gray-700">
                        Your Answer:
                      </label>
                    </div>
                    <textarea
                      value={answers[currentQuestionIndex] || ''}
                      onChange={(e) => handleAnswerSelect(e.target.value)}
                      placeholder="Type your answer here..."
                      className="w-full p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all"
                      rows="4"
                    />
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Navigation Footer */}
      <div className="bg-white border-t-2 border-gray-200 shadow-lg fixed bottom-0 left-0 right-0 z-50">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between gap-4">
            {/* Previous Button */}
            <button
              onClick={goToPrevious}
              disabled={currentQuestionIndex === 0}
              className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all ${
                currentQuestionIndex === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'
              }`}
            >
              <TbArrowLeft className="w-5 h-5" />
              <span>Previous</span>
            </button>

            {/* Question Navigation Dots */}
            <div className="flex items-center gap-2 overflow-x-auto max-w-md">
              {questions.map((_, index) => {
                const isAnswered = answers[index] !== undefined && answers[index] !== '';
                const isCurrent = index === currentQuestionIndex;
                return (
                  <button
                    key={index}
                    onClick={() => setCurrentQuestionIndex(index)}
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all ${
                      isCurrent
                        ? 'bg-blue-600 text-white scale-110'
                        : isAnswered
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
                    }`}
                  >
                    {isAnswered && !isCurrent ? (
                      <TbCheck className="w-4 h-4" />
                    ) : (
                      index + 1
                    )}
                  </button>
                );
              })}
            </div>

            {/* Next/Submit Button */}
            <button
              onClick={currentQuestionIndex === questions.length - 1 ? handleSubmitQuiz : goToNext}
              className="flex items-center gap-2 px-6 py-3 rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md"
            >
              <span>
                {currentQuestionIndex === questions.length - 1 ? 'Submit Quiz' : 'Next'}
              </span>
              {currentQuestionIndex === questions.length - 1 ? (
                <TbFlag className="w-5 h-5" />
              ) : (
                <TbArrowRight className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizPlay;
