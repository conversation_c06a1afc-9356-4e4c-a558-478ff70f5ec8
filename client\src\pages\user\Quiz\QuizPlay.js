import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { message } from 'antd';
import { getExamById } from '../../../apicalls/exams';
import { addReport } from '../../../apicalls/reports';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import { chatWithChatGPTToGetAns } from '../../../apicalls/chat';
import NewQuizRenderer from '../../../components/NewQuizRenderer';
import QuizErrorBoundary from '../../../components/QuizErrorBoundary';

const QuizPlay = () => {
  const [examData, setExamData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [secondsLeft, setSecondsLeft] = useState(0);
  const [timeUp, setTimeUp] = useState(false);
  const [intervalId, setIntervalId] = useState(null);
  const [startTime, setStartTime] = useState(null);
  const [forceUpdate, setForceUpdate] = useState(0);

  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);

  const getExamData = async (retryCount = 0) => {
    try {
      // DON'T show global loading - this causes white page
      // dispatch(ShowLoading());
      const response = await getExamById({ examId: id });
      // dispatch(HideLoading());

      if (response.success) {
        const questionsArray = response.data?.questions || [];

        console.log('🔍 Quiz Data Debug:', {
          examId: id,
          examName: response.data?.name,
          totalQuestions: questionsArray.length,
          firstQuestion: questionsArray[0],
          questionStructure: questionsArray.map(q => ({
            id: q?._id,
            name: q?.name,
            question: q?.question,
            type: q?.type,
            answerType: q?.answerType,
            hasOptions: q?.options ? Object.keys(q.options).length : 0
          }))
        });

        // Check if questions are properly populated
        if (questionsArray.length === 0) {
          console.warn('No questions found for this quiz');
          setQuestions([]);
          setExamData(response.data);
          return;
        }

        // Validate question structure with more detailed checking
        const validQuestions = questionsArray.filter(q => {
          if (!q) return false;

          // Check if question has text
          const hasQuestionText = q.name || q.question || q.text;
          if (!hasQuestionText) {
            console.warn('Question missing text:', q);
            return false;
          }

          // For MCQ questions, check if they have options
          if ((q.type === 'mcq' || q.answerType === 'Options') && (!q.options || Object.keys(q.options).length === 0)) {
            console.warn('MCQ question missing options:', q);
            return false;
          }

          return true;
        });

        console.log('✅ Valid questions found:', validQuestions.length);

        if (validQuestions.length === 0) {
          console.warn('No valid questions found for this quiz');
          setQuestions([]);
          setExamData(response.data);
          return;
        }

        // Set data immediately for instant rendering
        console.log('✅ Setting quiz data:', {
          examName: response.data?.name,
          questionsCount: validQuestions.length,
          firstQuestion: validQuestions[0]
        });

        setExamData(response.data);
        setQuestions(validQuestions);
        setSecondsLeft((response.data?.duration || 30) * 60);

        // Start timer immediately
        if (!startTime) {
          setStartTime(new Date());
        }

        // Force multiple re-renders to ensure questions show
        setForceUpdate(prev => prev + 1);
        setTimeout(() => setForceUpdate(prev => prev + 1), 100);
        setTimeout(() => setForceUpdate(prev => prev + 1), 500);
      } else {
        message.error(response.message);
        navigate('/user/quiz');
      }
    } catch (error) {
      // dispatch(HideLoading());

      // Retry logic for network errors
      if (retryCount < 2 && (error.code === 'ECONNABORTED' || !error.response)) {
        console.log(`Retrying quiz data fetch... Attempt ${retryCount + 1}`);
        setTimeout(() => getExamData(retryCount + 1), 1000);
        return;
      }

      message.error(error.message || 'Failed to load quiz. Please try again.');
      navigate('/user/quiz');
    }
  };

  const checkFreeTextAnswers = async (payload) => {
    if (!payload.length) return [];
    const { data } = await chatWithChatGPTToGetAns(payload);
    return data;
  };

  const calculateResult = useCallback(async () => {
    try {
      if (!user || !user._id) {
        message.error("User not found. Please log in again.");
        navigate("/login");
        return;
      }

      dispatch(ShowLoading());

      const freeTextPayload = [];
      questions.forEach((q, idx) => {
        if (q.type === "fill" || q.answerType === "Free Text" || q.answerType === "Fill in the Blank") {
          freeTextPayload.push({
            question: q.name,
            expectedAnswer: q.correctAnswer || q.correctOption,
            userAnswer: selectedOptions[idx] || "",
          });
        }
      });

      const gptResults = await checkFreeTextAnswers(freeTextPayload);
      const gptMap = {};

      gptResults.forEach((r) => {
        if (r.result && typeof r.result.isCorrect === "boolean") {
          gptMap[r.question] = r.result;
        } else if (typeof r.isCorrect === "boolean") {
          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || "" };
        }
      });

      const correctAnswers = [];
      const wrongAnswers = [];

      questions.forEach((q, idx) => {
        const userAnswerKey = selectedOptions[idx] || "";

        if (q.type === "fill" || q.answerType === "Free Text" || q.answerType === "Fill in the Blank") {
          const { isCorrect = false, reason = "" } = gptMap[q.name] || {};
          const enriched = { ...q, userAnswer: userAnswerKey, reason };

          if (isCorrect) {
            correctAnswers.push(enriched);
          } else {
            wrongAnswers.push(enriched);
          }
        } else if (q.type === "mcq" || q.answerType === "Options") {
          const correctKey = q.correctOption || q.correctAnswer;
          const isCorrect = correctKey === userAnswerKey;
          const enriched = { ...q, userAnswer: userAnswerKey };

          if (isCorrect) {
            correctAnswers.push(enriched);
          } else {
            wrongAnswers.push(enriched);
          }
        }
      });

      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;
      const totalTimeAllowed = (examData?.duration || 0) * 60;
      const totalQuestions = questions.length;
      const correctCount = correctAnswers.length;
      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);
      const points = correctCount * 10;

      // Handle both passingMarks and passingPercentage for backward compatibility
      const passingPercentage = examData.passingPercentage || examData.passingMarks || 70;
      const verdict = scorePercentage >= passingPercentage ? "Pass" : "Fail";

      const tempResult = {
        correctAnswers,
        wrongAnswers,
        verdict,
        score: scorePercentage,
        points: points,
        totalQuestions: totalQuestions,
        timeSpent: timeSpent,
        totalTimeAllowed: totalTimeAllowed
      };

      const response = await addReport({
        exam: id,
        result: tempResult,
        user: user._id,
      });

      if (response.success) {
        // Clear ranking cache for real-time updates
        localStorage.removeItem('rankingCache');
        localStorage.removeItem('userRankingPosition');
        localStorage.removeItem('leaderboardData');

        // Trigger ranking update event for real-time updates
        window.dispatchEvent(new CustomEvent('rankingUpdate', {
          detail: {
            userId: user._id,
            xpGained: response.xpData?.xpAwarded || 0,
            newTotalXP: response.xpData?.newTotalXP || 0,
            levelUp: response.xpData?.levelUp || false,
            newLevel: response.xpData?.newLevel || user.currentLevel
          }
        }));

        // Debug XP data
        console.log('🔍 Quiz completion response:', response);
        console.log('💰 XP Data received:', response.xpData);

        const resultWithXP = {
          ...tempResult,
          xpData: response.xpData
        };

        console.log('📊 Final result with XP:', resultWithXP);
        navigate(`/quiz/${id}/result`, { state: { result: resultWithXP } });
      } else {
        message.error(response.message);
        console.error('❌ Quiz submission failed:', response.message);
      }
      dispatch(HideLoading());

    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);

  const startTimer = useCallback(() => {
    const totalSeconds = (examData?.duration || 0) * 60;
    setSecondsLeft(totalSeconds);
    setStartTime(Date.now());

    const newIntervalId = setInterval(() => {
      setSecondsLeft((prevSeconds) => {
        if (prevSeconds > 0) {
          return prevSeconds - 1;
        } else {
          setTimeUp(true);
          return 0;
        }
      });
    }, 1000);
    setIntervalId(newIntervalId);
  }, [examData]);

  useEffect(() => {
    if (timeUp && intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
      calculateResult();
    }
  }, [timeUp, intervalId, calculateResult]);

  useEffect(() => {
    if (id) {
      // Start loading immediately without delay
      console.log('🚀 Component mounted, loading quiz data for ID:', id);
      getExamData();

      // Initialize timer immediately
      if (!startTime) {
        setStartTime(new Date());
      }
    }
  }, [id]);

  // Also trigger on component mount to ensure it runs
  useEffect(() => {
    if (id && questions.length === 0 && !examData) {
      console.log('🔄 Backup trigger - loading quiz data');
      getExamData();
    }
  }, []);

  // Force update when questions are loaded
  useEffect(() => {
    if (questions.length > 0) {
      console.log('🔄 Questions loaded, forcing update...');
      setForceUpdate(prev => prev + 1);
    }
  }, [questions.length]);

  useEffect(() => {
    document.body.classList.add('quiz-fullscreen');
    return () => {
      document.body.classList.remove('quiz-fullscreen');
    };
  }, []);

  useEffect(() => {
    if (examData && questions.length > 0) {
      startTimer();
    }
  }, [examData, questions]);

  useEffect(() => {
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
        setIntervalId(null);
      }
    };
  }, [intervalId]);

  // NEVER show loading screen - always render quiz interface
  // Create fallback exam data if needed
  const safeExamData = examData || {
    _id: id,
    name: 'Loading Quiz...',
    duration: 30,
    questions: []
  };

  // Create fallback questions if needed - make them look like real questions
  const safeQuestions = questions.length > 0 ? questions : [
    {
      _id: 'loading-1',
      name: 'Sample Question',
      question: 'Questions are loading... Please wait a moment while we prepare your quiz.',
      type: 'mcq',
      options: {
        A: 'Questions are being loaded...',
        B: 'Please wait while we fetch your quiz...',
        C: 'Your quiz will appear shortly...',
        D: 'Loading quiz content...'
      }
    },
    {
      _id: 'loading-2',
      name: 'Loading Question 2',
      question: 'This is a placeholder question while your actual quiz loads.',
      type: 'mcq',
      options: {
        A: 'Option A will load soon...',
        B: 'Option B is being prepared...',
        C: 'Option C will appear shortly...',
        D: 'Option D is loading...'
      }
    }
  ];

  // Ensure timer is always initialized
  if (secondsLeft === 0 && safeExamData.duration) {
    setSecondsLeft(safeExamData.duration * 60);
  }



  // Handle case where selectedQuestionIndex is out of bounds
  if (safeQuestions.length > 0 && selectedQuestionIndex >= safeQuestions.length) {
    setSelectedQuestionIndex(0);
  }

  // Always use safe questions - never show loading
  const currentQuestion = safeQuestions[selectedQuestionIndex] || safeQuestions[0];

  // Always use safe data - never show loading
  const activeQuestion = currentQuestion;

  // Final debug before rendering
  console.log('🎯 Final QuizPlay render state:', {
    examData: examData ? { name: examData.name, id: examData._id } : null,
    questionsCount: questions.length,
    safeQuestionsCount: safeQuestions.length,
    selectedQuestionIndex,
    currentQuestion: currentQuestion ? {
      id: currentQuestion._id,
      name: currentQuestion.name?.substring(0, 50) + '...',
      type: currentQuestion.type,
      hasOptions: currentQuestion.options ? Object.keys(currentQuestion.options).length : 0
    } : null,
    timeLeft: secondsLeft,
    activeQuestion: activeQuestion
  });
  const mountKey = `quiz-${safeExamData._id}-${selectedQuestionIndex}-${forceUpdate}-${safeQuestions.length}`;

  return (
    <QuizErrorBoundary key={mountKey}>
      <NewQuizRenderer
        key={mountKey}
        question={activeQuestion}
        questionIndex={selectedQuestionIndex}
        totalQuestions={safeQuestions.length}
        selectedAnswer={selectedOptions[selectedQuestionIndex]}
        selectedOptions={selectedOptions} // Pass all selected options for navigation indicators
        onAnswerChange={(answer) => {
          console.log('💾 Saving answer for question', selectedQuestionIndex + 1, ':', answer);
          const newSelectedOptions = {
            ...selectedOptions,
            [selectedQuestionIndex]: answer,
          };
          setSelectedOptions(newSelectedOptions);
          console.log('📋 All answers so far:', newSelectedOptions);
        }}
        timeLeft={secondsLeft}
        examTitle={safeExamData.name || "Quiz"}
        isTimeWarning={secondsLeft <= 60}
        onNext={() => {
          console.log('➡️ Next button clicked, current index:', selectedQuestionIndex, 'total:', safeQuestions.length);
          if (selectedQuestionIndex === safeQuestions.length - 1) {
            console.log('🏁 Last question reached, calculating result...');
            calculateResult();
          } else {
            console.log('📝 Moving to next question:', selectedQuestionIndex + 1);
            setSelectedQuestionIndex(selectedQuestionIndex + 1);
          }
        }}
        onPrevious={() => {
          console.log('🔙 Previous button clicked, current index:', selectedQuestionIndex);
          if (selectedQuestionIndex > 0) {
            setSelectedQuestionIndex(selectedQuestionIndex - 1);
          }
        }}
        onSubmit={() => {
          console.log('🏁 Submit button clicked, calculating result...');
          calculateResult();
        }}
      />
    </QuizErrorBoundary>
  );
};

export default QuizPlay;
