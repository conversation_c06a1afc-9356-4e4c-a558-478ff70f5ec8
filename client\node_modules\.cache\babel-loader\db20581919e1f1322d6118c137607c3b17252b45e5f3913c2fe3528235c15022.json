{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\NewQuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck, TbX, TbPhoto, TbEdit, TbCircleCheck, TbCircle } from 'react-icons/tb';\n\n// Utility function to extract and normalize question data\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst extractQuestionData = question => {\n  if (!question) return null;\n  const questionText = question.name || question.question || question.text || question.title || '';\n  const questionType = question.type || question.answerType || question.questionType || 'mcq';\n  const questionOptions = question.options || question.choices || question.answers || {};\n  const correctAnswer = question.correctAnswer || question.correctOption || '';\n  const imageUrl = question.imageUrl || question.image || '';\n\n  // Normalize question type\n  let normalizedType = 'mcq';\n  const typeString = String(questionType).toLowerCase();\n  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {\n    normalizedType = 'mcq';\n  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {\n    normalizedType = 'fill';\n  } else if (typeString.includes('image') || typeString.includes('picture') || typeString.includes('diagram')) {\n    normalizedType = 'image';\n  }\n\n  // If no options are provided but it's marked as MCQ, treat as fill-in\n  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {\n    normalizedType = 'fill';\n  }\n  return {\n    text: questionText,\n    type: normalizedType,\n    options: questionOptions,\n    correctAnswer,\n    imageUrl,\n    originalQuestion: question\n  };\n};\n\n// Format time utility\nconst formatTime = seconds => {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n};\nconst NewQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false,\n  onSubmit\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n  const questionData = question ? extractQuestionData(question) : null;\n\n  // Update current answer when selectedAnswer changes\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Handle answer selection\n  const handleAnswerChange = useCallback(answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(!!answer);\n    onAnswerChange(answer);\n  }, [onAnswerChange]);\n\n  // Handle navigation\n  const handleNext = useCallback(() => {\n    if (questionIndex === totalQuestions - 1) {\n      // Last question - submit quiz\n      if (onSubmit) {\n        onSubmit();\n      }\n    } else {\n      onNext();\n    }\n  }, [questionIndex, totalQuestions, onNext, onSubmit]);\n  const handlePrevious = useCallback(() => {\n    if (questionIndex > 0) {\n      onPrevious();\n    }\n  }, [questionIndex, onPrevious]);\n  if (!questionData) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-6xl mb-4\",\n          children: \"\\u2753\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-xl font-bold text-gray-900 mb-2\",\n          children: \"Question not available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 114,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Please check your connection and try again.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-lg sm:text-xl font-bold text-gray-900 truncate\",\n              children: examTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mt-1\",\n              children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-2 px-4 py-2 rounded-full font-bold text-sm sm:text-base transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 animate-pulse' : 'bg-blue-100 text-blue-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(motion.div, {\n              className: \"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full\",\n              initial: {\n                width: 0\n              },\n              animate: {\n                width: `${(questionIndex + 1) / totalQuestions * 100}%`\n              },\n              transition: {\n                duration: 0.5,\n                ease: \"easeOut\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-xs text-gray-500 mt-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [Math.round((questionIndex + 1) / totalQuestions * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 max-w-4xl mx-auto w-full px-4 py-6 sm:py-8\",\n      children: /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 20\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          exit: {\n            opacity: 0,\n            x: -20\n          },\n          transition: {\n            duration: 0.3\n          },\n          className: \"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `px-3 py-1 rounded-full text-xs font-semibold ${questionData.type === 'mcq' ? 'bg-blue-100 text-blue-800' : questionData.type === 'fill' ? 'bg-green-100 text-green-800' : 'bg-purple-100 text-purple-800'}`,\n                children: questionData.type === 'mcq' ? 'Multiple Choice' : questionData.type === 'fill' ? 'Fill in the Blank' : 'Image Question'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), isAnswered && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center gap-1 text-white text-sm font-medium\",\n                children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 21\n                }, this), \"Answered\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6 sm:p-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-xl sm:text-2xl font-semibold text-gray-900 mb-4 leading-relaxed\",\n                children: questionData.text\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 17\n              }, this), questionData.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-6\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative bg-gray-100 rounded-xl overflow-hidden\",\n                  children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                    src: questionData.imageUrl,\n                    alt: \"Question diagram\",\n                    className: \"w-full max-w-md mx-auto h-auto object-contain\",\n                    onError: e => {\n                      e.target.style.display = 'none';\n                      e.target.nextSibling.style.display = 'flex';\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hidden items-center justify-center h-48 text-gray-500\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-center\",\n                      children: [/*#__PURE__*/_jsxDEV(TbPhoto, {\n                        className: \"w-12 h-12 mx-auto mb-2\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 223,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"Image not available\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 224,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: questionData.type === 'mcq' && questionData.options && Object.keys(questionData.options).length > 0 ?\n              // Multiple Choice Questions\n              Object.entries(questionData.options).map(([key, value]) => {\n                const isSelected = currentAnswer === key;\n                return /*#__PURE__*/_jsxDEV(motion.button, {\n                  onClick: () => handleAnswerChange(key),\n                  className: `w-full p-4 rounded-xl border-2 text-left transition-all duration-200 ${isSelected ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'}`,\n                  whileHover: {\n                    scale: 1.02\n                  },\n                  whileTap: {\n                    scale: 0.98\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-600'}`,\n                      children: isSelected ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                        className: \"w-5 h-5\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 256,\n                        columnNumber: 43\n                      }, this) : key\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 251,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-gray-900 font-medium\",\n                      children: value\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 258,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 25\n                  }, this)\n                }, key, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 23\n                }, this);\n              }) :\n              /*#__PURE__*/\n              // Fill-in-the-Blank Questions\n              _jsxDEV(\"div\", {\n                className: \"space-y-4\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border-2 border-green-200\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-2 mb-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                      children: /*#__PURE__*/_jsxDEV(TbEdit, {\n                        className: \"w-5 h-5 text-white\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 269,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"text-sm font-semibold text-gray-700\",\n                      children: \"Your Answer:\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 267,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: currentAnswer,\n                    onChange: e => handleAnswerChange(e.target.value),\n                    placeholder: \"Type your answer here...\",\n                    className: \"w-full p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all\",\n                    rows: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 275,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, questionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white border-t-2 border-gray-200 shadow-lg sticky bottom-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-4xl mx-auto px-4 py-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handlePrevious,\n            disabled: questionIndex === 0,\n            className: `flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all ${questionIndex === 0 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbArrowLeft, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: \"Previous\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 overflow-x-auto max-w-xs sm:max-w-md\",\n            children: Array.from({\n              length: totalQuestions\n            }, (_, index) => {\n              const isAnswered = selectedOptions[index] !== undefined && selectedOptions[index] !== '';\n              const isCurrent = index === questionIndex;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all ${isCurrent ? 'bg-blue-600 text-white scale-110' : isAnswered ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-600'}`,\n                children: isAnswered && !isCurrent ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                  className: \"w-4 h-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 23\n                }, this) : index + 1\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 19\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleNext,\n            className: \"flex items-center gap-2 px-6 py-3 rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hidden sm:inline\",\n              children: questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 15\n            }, this), questionIndex === totalQuestions - 1 ? /*#__PURE__*/_jsxDEV(TbCheck, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(TbArrowRight, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n};\n_s(NewQuizRenderer, \"Een+6LZ8EzcaOjXF8vueQPL4/Ts=\");\n_c = NewQuizRenderer;\nexport default NewQuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"NewQuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "AnimatePresence", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbX", "TbPhoto", "TbEdit", "TbCircleCheck", "TbCircle", "jsxDEV", "_jsxDEV", "extractQuestionData", "question", "questionText", "name", "text", "title", "questionType", "type", "answerType", "questionOptions", "options", "choices", "answers", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "imageUrl", "image", "normalizedType", "typeString", "String", "toLowerCase", "includes", "Object", "keys", "length", "originalQuestion", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "selectedOptions", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "onSubmit", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "questionData", "handleAnswerChange", "answer", "handleNext", "handlePrevious", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "width", "animate", "transition", "duration", "ease", "round", "mode", "opacity", "x", "exit", "src", "alt", "onError", "e", "target", "style", "display", "nextS<PERSON>ling", "entries", "map", "key", "value", "isSelected", "button", "onClick", "whileHover", "scale", "whileTap", "onChange", "placeholder", "rows", "disabled", "Array", "from", "_", "index", "undefined", "isCurrent", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/NewQuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { \n  Tb<PERSON>lock, \n  TbArrowLeft, \n  TbArrowRight, \n  TbCheck, \n  TbX,\n  TbPhoto,\n  TbEdit,\n  TbCircleCheck,\n  TbCircle\n} from 'react-icons/tb';\n\n// Utility function to extract and normalize question data\nconst extractQuestionData = (question) => {\n  if (!question) return null;\n\n  const questionText = question.name || question.question || question.text || question.title || '';\n  const questionType = question.type || question.answerType || question.questionType || 'mcq';\n  const questionOptions = question.options || question.choices || question.answers || {};\n  const correctAnswer = question.correctAnswer || question.correctOption || '';\n  const imageUrl = question.imageUrl || question.image || '';\n\n  // Normalize question type\n  let normalizedType = 'mcq';\n  const typeString = String(questionType).toLowerCase();\n\n  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {\n    normalizedType = 'mcq';\n  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {\n    normalizedType = 'fill';\n  } else if (typeString.includes('image') || typeString.includes('picture') || typeString.includes('diagram')) {\n    normalizedType = 'image';\n  }\n\n  // If no options are provided but it's marked as MCQ, treat as fill-in\n  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {\n    normalizedType = 'fill';\n  }\n\n  return {\n    text: questionText,\n    type: normalizedType,\n    options: questionOptions,\n    correctAnswer,\n    imageUrl,\n    originalQuestion: question\n  };\n};\n\n// Format time utility\nconst formatTime = (seconds) => {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n};\n\nconst NewQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false,\n  onSubmit\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  const questionData = question ? extractQuestionData(question) : null;\n\n  // Update current answer when selectedAnswer changes\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Handle answer selection\n  const handleAnswerChange = useCallback((answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(!!answer);\n    onAnswerChange(answer);\n  }, [onAnswerChange]);\n\n  // Handle navigation\n  const handleNext = useCallback(() => {\n    if (questionIndex === totalQuestions - 1) {\n      // Last question - submit quiz\n      if (onSubmit) {\n        onSubmit();\n      }\n    } else {\n      onNext();\n    }\n  }, [questionIndex, totalQuestions, onNext, onSubmit]);\n\n  const handlePrevious = useCallback(() => {\n    if (questionIndex > 0) {\n      onPrevious();\n    }\n  }, [questionIndex, onPrevious]);\n\n  if (!questionData) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"text-6xl mb-4\">❓</div>\n          <h2 className=\"text-xl font-bold text-gray-900 mb-2\">Question not available</h2>\n          <p className=\"text-gray-600\">Please check your connection and try again.</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col\">\n      {/* Header with Timer and Quiz Info */}\n      <div className=\"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Quiz Title and Question Counter */}\n            <div className=\"flex-1\">\n              <h1 className=\"text-lg sm:text-xl font-bold text-gray-900 truncate\">\n                {examTitle}\n              </h1>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                Question {questionIndex + 1} of {totalQuestions}\n              </p>\n            </div>\n\n            {/* Timer */}\n            <div className={`flex items-center gap-2 px-4 py-2 rounded-full font-bold text-sm sm:text-base transition-all ${\n              isTimeWarning \n                ? 'bg-red-100 text-red-700 animate-pulse' \n                : 'bg-blue-100 text-blue-700'\n            }`}>\n              <TbClock className=\"w-5 h-5\" />\n              <span>{formatTime(timeLeft)}</span>\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mt-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: `${((questionIndex + 1) / totalQuestions) * 100}%` }}\n                transition={{ duration: 0.5, ease: \"easeOut\" }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n              <span>Progress</span>\n              <span>{Math.round(((questionIndex + 1) / totalQuestions) * 100)}%</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"flex-1 max-w-4xl mx-auto w-full px-4 py-6 sm:py-8\">\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={questionIndex}\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: -20 }}\n            transition={{ duration: 0.3 }}\n            className=\"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden\"\n          >\n            {/* Question Header */}\n            <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4\">\n              <div className=\"flex items-center justify-between\">\n                <div className={`px-3 py-1 rounded-full text-xs font-semibold ${\n                  questionData.type === 'mcq'\n                    ? 'bg-blue-100 text-blue-800'\n                    : questionData.type === 'fill'\n                    ? 'bg-green-100 text-green-800'\n                    : 'bg-purple-100 text-purple-800'\n                }`}>\n                  {questionData.type === 'mcq' ? 'Multiple Choice' :\n                   questionData.type === 'fill' ? 'Fill in the Blank' :\n                   'Image Question'}\n                </div>\n                {isAnswered && (\n                  <div className=\"flex items-center gap-1 text-white text-sm font-medium\">\n                    <TbCheck className=\"w-4 h-4\" />\n                    Answered\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Question Content */}\n            <div className=\"p-6 sm:p-8\">\n              {/* Question Text */}\n              <div className=\"mb-8\">\n                <h2 className=\"text-xl sm:text-2xl font-semibold text-gray-900 mb-4 leading-relaxed\">\n                  {questionData.text}\n                </h2>\n                \n                {/* Image for image-based questions */}\n                {questionData.imageUrl && (\n                  <div className=\"mb-6\">\n                    <div className=\"relative bg-gray-100 rounded-xl overflow-hidden\">\n                      <img\n                        src={questionData.imageUrl}\n                        alt=\"Question diagram\"\n                        className=\"w-full max-w-md mx-auto h-auto object-contain\"\n                        onError={(e) => {\n                          e.target.style.display = 'none';\n                          e.target.nextSibling.style.display = 'flex';\n                        }}\n                      />\n                      <div className=\"hidden items-center justify-center h-48 text-gray-500\">\n                        <div className=\"text-center\">\n                          <TbPhoto className=\"w-12 h-12 mx-auto mb-2\" />\n                          <p>Image not available</p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              {/* Answer Options */}\n              <div className=\"space-y-4\">\n                {questionData.type === 'mcq' && questionData.options && Object.keys(questionData.options).length > 0 ? (\n                  // Multiple Choice Questions\n                  Object.entries(questionData.options).map(([key, value]) => {\n                    const isSelected = currentAnswer === key;\n                    return (\n                      <motion.button\n                        key={key}\n                        onClick={() => handleAnswerChange(key)}\n                        className={`w-full p-4 rounded-xl border-2 text-left transition-all duration-200 ${\n                          isSelected\n                            ? 'border-blue-500 bg-blue-50 shadow-md'\n                            : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'\n                        }`}\n                        whileHover={{ scale: 1.02 }}\n                        whileTap={{ scale: 0.98 }}\n                      >\n                        <div className=\"flex items-center gap-4\">\n                          <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold ${\n                            isSelected\n                              ? 'border-blue-500 bg-blue-500 text-white'\n                              : 'border-gray-300 text-gray-600'\n                          }`}>\n                            {isSelected ? <TbCheck className=\"w-5 h-5\" /> : key}\n                          </div>\n                          <span className=\"text-gray-900 font-medium\">{value}</span>\n                        </div>\n                      </motion.button>\n                    );\n                  })\n                ) : (\n                  // Fill-in-the-Blank Questions\n                  <div className=\"space-y-4\">\n                    <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border-2 border-green-200\">\n                      <div className=\"flex items-center gap-2 mb-4\">\n                        <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n                          <TbEdit className=\"w-5 h-5 text-white\" />\n                        </div>\n                        <label className=\"text-sm font-semibold text-gray-700\">\n                          Your Answer:\n                        </label>\n                      </div>\n                      <textarea\n                        value={currentAnswer}\n                        onChange={(e) => handleAnswerChange(e.target.value)}\n                        placeholder=\"Type your answer here...\"\n                        className=\"w-full p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all\"\n                        rows=\"3\"\n                      />\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          </motion.div>\n        </AnimatePresence>\n      </div>\n\n      {/* Navigation Footer */}\n      <div className=\"bg-white border-t-2 border-gray-200 shadow-lg sticky bottom-0 z-50\">\n        <div className=\"max-w-4xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between gap-4\">\n            {/* Previous Button */}\n            <button\n              onClick={handlePrevious}\n              disabled={questionIndex === 0}\n              className={`flex items-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all ${\n                questionIndex === 0\n                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'\n                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'\n              }`}\n            >\n              <TbArrowLeft className=\"w-5 h-5\" />\n              <span className=\"hidden sm:inline\">Previous</span>\n            </button>\n\n            {/* Question Navigation Dots */}\n            <div className=\"flex items-center gap-2 overflow-x-auto max-w-xs sm:max-w-md\">\n              {Array.from({ length: totalQuestions }, (_, index) => {\n                const isAnswered = selectedOptions[index] !== undefined && selectedOptions[index] !== '';\n                const isCurrent = index === questionIndex;\n\n                return (\n                  <div\n                    key={index}\n                    className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold transition-all ${\n                      isCurrent\n                        ? 'bg-blue-600 text-white scale-110'\n                        : isAnswered\n                        ? 'bg-green-500 text-white'\n                        : 'bg-gray-200 text-gray-600'\n                    }`}\n                  >\n                    {isAnswered && !isCurrent ? (\n                      <TbCheck className=\"w-4 h-4\" />\n                    ) : (\n                      index + 1\n                    )}\n                  </div>\n                );\n              })}\n            </div>\n\n            {/* Next/Submit Button */}\n            <button\n              onClick={handleNext}\n              className=\"flex items-center gap-2 px-6 py-3 rounded-xl font-semibold bg-blue-600 hover:bg-blue-700 text-white transition-all hover:shadow-md\"\n            >\n              <span className=\"hidden sm:inline\">\n                {questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'}\n              </span>\n              {questionIndex === totalQuestions - 1 ? (\n                <TbCheck className=\"w-5 h-5\" />\n              ) : (\n                <TbArrowRight className=\"w-5 h-5\" />\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NewQuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,aAAa,EACbC,QAAQ,QACH,gBAAgB;;AAEvB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAIC,QAAQ,IAAK;EACxC,IAAI,CAACA,QAAQ,EAAE,OAAO,IAAI;EAE1B,MAAMC,YAAY,GAAGD,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACA,QAAQ,IAAIA,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACI,KAAK,IAAI,EAAE;EAChG,MAAMC,YAAY,GAAGL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACO,UAAU,IAAIP,QAAQ,CAACK,YAAY,IAAI,KAAK;EAC3F,MAAMG,eAAe,GAAGR,QAAQ,CAACS,OAAO,IAAIT,QAAQ,CAACU,OAAO,IAAIV,QAAQ,CAACW,OAAO,IAAI,CAAC,CAAC;EACtF,MAAMC,aAAa,GAAGZ,QAAQ,CAACY,aAAa,IAAIZ,QAAQ,CAACa,aAAa,IAAI,EAAE;EAC5E,MAAMC,QAAQ,GAAGd,QAAQ,CAACc,QAAQ,IAAId,QAAQ,CAACe,KAAK,IAAI,EAAE;;EAE1D;EACA,IAAIC,cAAc,GAAG,KAAK;EAC1B,MAAMC,UAAU,GAAGC,MAAM,CAACb,YAAY,CAAC,CAACc,WAAW,CAAC,CAAC;EAErD,IAAIF,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,IAAIH,UAAU,KAAK,KAAK,IAAIA,UAAU,CAACG,QAAQ,CAAC,UAAU,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,EAAE;IAC7HJ,cAAc,GAAG,KAAK;EACxB,CAAC,MAAM,IAAIC,UAAU,CAACG,QAAQ,CAAC,MAAM,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,UAAU,KAAK,MAAM,IAAIA,UAAU,CAACG,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC9HJ,cAAc,GAAG,MAAM;EACzB,CAAC,MAAM,IAAIC,UAAU,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,SAAS,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,SAAS,CAAC,EAAE;IAC3GJ,cAAc,GAAG,OAAO;EAC1B;;EAEA;EACA,IAAIA,cAAc,KAAK,KAAK,KAAK,CAACR,eAAe,IAAIa,MAAM,CAACC,IAAI,CAACd,eAAe,CAAC,CAACe,MAAM,KAAK,CAAC,CAAC,EAAE;IAC/FP,cAAc,GAAG,MAAM;EACzB;EAEA,OAAO;IACLb,IAAI,EAAEF,YAAY;IAClBK,IAAI,EAAEU,cAAc;IACpBP,OAAO,EAAED,eAAe;IACxBI,aAAa;IACbE,QAAQ;IACRU,gBAAgB,EAAExB;EACpB,CAAC;AACH,CAAC;;AAED;AACA,MAAMyB,UAAU,GAAIC,OAAO,IAAK;EAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;EACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;EACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;AACrE,CAAC;AAED,MAAMC,eAAe,GAAGA,CAAC;EACvBjC,QAAQ;EACRkC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,eAAe,GAAG,CAAC,CAAC;EACpBC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG,KAAK;EACrBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhE,QAAQ,CAACqD,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMmE,YAAY,GAAGlD,QAAQ,GAAGD,mBAAmB,CAACC,QAAQ,CAAC,GAAG,IAAI;;EAEpE;EACAhB,SAAS,CAAC,MAAM;IACd+D,gBAAgB,CAACX,cAAc,IAAI,EAAE,CAAC;IACtCa,aAAa,CAAC,CAAC,CAACb,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;;EAEnC;EACA,MAAMiB,kBAAkB,GAAGlE,WAAW,CAAEmE,MAAM,IAAK;IACjDL,gBAAgB,CAACK,MAAM,CAAC;IACxBH,aAAa,CAAC,CAAC,CAACG,MAAM,CAAC;IACvBd,cAAc,CAACc,MAAM,CAAC;EACxB,CAAC,EAAE,CAACd,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMe,UAAU,GAAGpE,WAAW,CAAC,MAAM;IACnC,IAAIiD,aAAa,KAAKC,cAAc,GAAG,CAAC,EAAE;MACxC;MACA,IAAIS,QAAQ,EAAE;QACZA,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,MAAM;MACLJ,MAAM,CAAC,CAAC;IACV;EACF,CAAC,EAAE,CAACN,aAAa,EAAEC,cAAc,EAAEK,MAAM,EAAEI,QAAQ,CAAC,CAAC;EAErD,MAAMU,cAAc,GAAGrE,WAAW,CAAC,MAAM;IACvC,IAAIiD,aAAa,GAAG,CAAC,EAAE;MACrBO,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACP,aAAa,EAAEO,UAAU,CAAC,CAAC;EAE/B,IAAI,CAACS,YAAY,EAAE;IACjB,oBACEpD,OAAA;MAAKyD,SAAS,EAAC,4FAA4F;MAAAC,QAAA,eACzG1D,OAAA;QAAKyD,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B1D,OAAA;UAAKyD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtC9D,OAAA;UAAIyD,SAAS,EAAC,sCAAsC;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChF9D,OAAA;UAAGyD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA2C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE9D,OAAA;IAAKyD,SAAS,EAAC,yEAAyE;IAAAC,QAAA,gBAEtF1D,OAAA;MAAKyD,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9E1D,OAAA;QAAKyD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1C1D,OAAA;UAAKyD,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAEhD1D,OAAA;YAAKyD,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrB1D,OAAA;cAAIyD,SAAS,EAAC,qDAAqD;cAAAC,QAAA,EAChEd;YAAS;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACL9D,OAAA;cAAGyD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,WAC/B,EAACtB,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGN9D,OAAA;YAAKyD,SAAS,EAAG,gGACfZ,aAAa,GACT,uCAAuC,GACvC,2BACL,EAAE;YAAAa,QAAA,gBACD1D,OAAA,CAACV,OAAO;cAACmE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B9D,OAAA;cAAA0D,QAAA,EAAO/B,UAAU,CAACc,QAAQ;YAAC;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9D,OAAA;UAAKyD,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnB1D,OAAA;YAAKyD,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClD1D,OAAA,CAACZ,MAAM,CAAC2E,GAAG;cACTN,SAAS,EAAC,+DAA+D;cACzEO,OAAO,EAAE;gBAAEC,KAAK,EAAE;cAAE,CAAE;cACtBC,OAAO,EAAE;gBAAED,KAAK,EAAG,GAAG,CAAC7B,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAI;cAAG,CAAE;cACvE8B,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEC,IAAI,EAAE;cAAU;YAAE;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN9D,OAAA;YAAKyD,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9D1D,OAAA;cAAA0D,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrB9D,OAAA;cAAA0D,QAAA,GAAO5B,IAAI,CAACwC,KAAK,CAAE,CAAClC,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG,CAAC,EAAC,GAAC;YAAA;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,mDAAmD;MAAAC,QAAA,eAChE1D,OAAA,CAACX,eAAe;QAACkF,IAAI,EAAC,MAAM;QAAAb,QAAA,eAC1B1D,OAAA,CAACZ,MAAM,CAAC2E,GAAG;UAETC,OAAO,EAAE;YAAEQ,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BP,OAAO,EAAE;YAAEM,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BC,IAAI,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BX,SAAS,EAAC,uEAAuE;UAAAC,QAAA,gBAGjF1D,OAAA;YAAKyD,SAAS,EAAC,wDAAwD;YAAAC,QAAA,eACrE1D,OAAA;cAAKyD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD1D,OAAA;gBAAKyD,SAAS,EAAG,gDACfL,YAAY,CAAC5C,IAAI,KAAK,KAAK,GACvB,2BAA2B,GAC3B4C,YAAY,CAAC5C,IAAI,KAAK,MAAM,GAC5B,6BAA6B,GAC7B,+BACL,EAAE;gBAAAkD,QAAA,EACAN,YAAY,CAAC5C,IAAI,KAAK,KAAK,GAAG,iBAAiB,GAC/C4C,YAAY,CAAC5C,IAAI,KAAK,MAAM,GAAG,mBAAmB,GAClD;cAAgB;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC,EACLZ,UAAU,iBACTlD,OAAA;gBAAKyD,SAAS,EAAC,wDAAwD;gBAAAC,QAAA,gBACrE1D,OAAA,CAACP,OAAO;kBAACgE,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,YAEjC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9D,OAAA;YAAKyD,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAEzB1D,OAAA;cAAKyD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnB1D,OAAA;gBAAIyD,SAAS,EAAC,sEAAsE;gBAAAC,QAAA,EACjFN,YAAY,CAAC/C;cAAI;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB,CAAC,EAGJV,YAAY,CAACpC,QAAQ,iBACpBhB,OAAA;gBAAKyD,SAAS,EAAC,MAAM;gBAAAC,QAAA,eACnB1D,OAAA;kBAAKyD,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,gBAC9D1D,OAAA;oBACE2E,GAAG,EAAEvB,YAAY,CAACpC,QAAS;oBAC3B4D,GAAG,EAAC,kBAAkB;oBACtBnB,SAAS,EAAC,+CAA+C;oBACzDoB,OAAO,EAAGC,CAAC,IAAK;sBACdA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;sBAC/BH,CAAC,CAACC,MAAM,CAACG,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,MAAM;oBAC7C;kBAAE;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACF9D,OAAA;oBAAKyD,SAAS,EAAC,uDAAuD;oBAAAC,QAAA,eACpE1D,OAAA;sBAAKyD,SAAS,EAAC,aAAa;sBAAAC,QAAA,gBAC1B1D,OAAA,CAACL,OAAO;wBAAC8D,SAAS,EAAC;sBAAwB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC9C9D,OAAA;wBAAA0D,QAAA,EAAG;sBAAmB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN9D,OAAA;cAAKyD,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBN,YAAY,CAAC5C,IAAI,KAAK,KAAK,IAAI4C,YAAY,CAACzC,OAAO,IAAIY,MAAM,CAACC,IAAI,CAAC4B,YAAY,CAACzC,OAAO,CAAC,CAACc,MAAM,GAAG,CAAC;cAClG;cACAF,MAAM,CAAC4D,OAAO,CAAC/B,YAAY,CAACzC,OAAO,CAAC,CAACyE,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;gBACzD,MAAMC,UAAU,GAAGvC,aAAa,KAAKqC,GAAG;gBACxC,oBACErF,OAAA,CAACZ,MAAM,CAACoG,MAAM;kBAEZC,OAAO,EAAEA,CAAA,KAAMpC,kBAAkB,CAACgC,GAAG,CAAE;kBACvC5B,SAAS,EAAG,wEACV8B,UAAU,GACN,sCAAsC,GACtC,iEACL,EAAE;kBACHG,UAAU,EAAE;oBAAEC,KAAK,EAAE;kBAAK,CAAE;kBAC5BC,QAAQ,EAAE;oBAAED,KAAK,EAAE;kBAAK,CAAE;kBAAAjC,QAAA,eAE1B1D,OAAA;oBAAKyD,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtC1D,OAAA;sBAAKyD,SAAS,EAAG,4EACf8B,UAAU,GACN,wCAAwC,GACxC,+BACL,EAAE;sBAAA7B,QAAA,EACA6B,UAAU,gBAAGvF,OAAA,CAACP,OAAO;wBAACgE,SAAS,EAAC;sBAAS;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,GAAGuB;oBAAG;sBAAA1B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChD,CAAC,eACN9D,OAAA;sBAAMyD,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAE4B;oBAAK;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD;gBAAC,GAnBDuB,GAAG;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBK,CAAC;cAEpB,CAAC,CAAC;cAAA;cAEF;cACA9D,OAAA;gBAAKyD,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACxB1D,OAAA;kBAAKyD,SAAS,EAAC,uFAAuF;kBAAAC,QAAA,gBACpG1D,OAAA;oBAAKyD,SAAS,EAAC,8BAA8B;oBAAAC,QAAA,gBAC3C1D,OAAA;sBAAKyD,SAAS,EAAC,oEAAoE;sBAAAC,QAAA,eACjF1D,OAAA,CAACJ,MAAM;wBAAC6D,SAAS,EAAC;sBAAoB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtC,CAAC,eACN9D,OAAA;sBAAOyD,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAAC;oBAEvD;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACN9D,OAAA;oBACEsF,KAAK,EAAEtC,aAAc;oBACrB6C,QAAQ,EAAGf,CAAC,IAAKzB,kBAAkB,CAACyB,CAAC,CAACC,MAAM,CAACO,KAAK,CAAE;oBACpDQ,WAAW,EAAC,0BAA0B;oBACtCrC,SAAS,EAAC,kJAAkJ;oBAC5JsC,IAAI,EAAC;kBAAG;oBAAApC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GApHD1B,aAAa;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqHR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eAGN9D,OAAA;MAAKyD,SAAS,EAAC,oEAAoE;MAAAC,QAAA,eACjF1D,OAAA;QAAKyD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1C1D,OAAA;UAAKyD,SAAS,EAAC,yCAAyC;UAAAC,QAAA,gBAEtD1D,OAAA;YACEyF,OAAO,EAAEjC,cAAe;YACxBwC,QAAQ,EAAE5D,aAAa,KAAK,CAAE;YAC9BqB,SAAS,EAAG,6EACVrB,aAAa,KAAK,CAAC,GACf,8CAA8C,GAC9C,6DACL,EAAE;YAAAsB,QAAA,gBAEH1D,OAAA,CAACT,WAAW;cAACkE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnC9D,OAAA;cAAMyD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eAGT9D,OAAA;YAAKyD,SAAS,EAAC,8DAA8D;YAAAC,QAAA,EAC1EuC,KAAK,CAACC,IAAI,CAAC;cAAEzE,MAAM,EAAEY;YAAe,CAAC,EAAE,CAAC8D,CAAC,EAAEC,KAAK,KAAK;cACpD,MAAMlD,UAAU,GAAGX,eAAe,CAAC6D,KAAK,CAAC,KAAKC,SAAS,IAAI9D,eAAe,CAAC6D,KAAK,CAAC,KAAK,EAAE;cACxF,MAAME,SAAS,GAAGF,KAAK,KAAKhE,aAAa;cAEzC,oBACEpC,OAAA;gBAEEyD,SAAS,EAAG,0FACV6C,SAAS,GACL,kCAAkC,GAClCpD,UAAU,GACV,yBAAyB,GACzB,2BACL,EAAE;gBAAAQ,QAAA,EAEFR,UAAU,IAAI,CAACoD,SAAS,gBACvBtG,OAAA,CAACP,OAAO;kBAACgE,SAAS,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,GAE/BsC,KAAK,GAAG;cACT,GAbIA,KAAK;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcP,CAAC;YAEV,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN9D,OAAA;YACEyF,OAAO,EAAElC,UAAW;YACpBE,SAAS,EAAC,oIAAoI;YAAAC,QAAA,gBAE9I1D,OAAA;cAAMyD,SAAS,EAAC,kBAAkB;cAAAC,QAAA,EAC/BtB,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,QAAQ,GAAG;YAAM;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC,EACN1B,aAAa,KAAKC,cAAc,GAAG,CAAC,gBACnCrC,OAAA,CAACP,OAAO;cAACgE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAE/B9D,OAAA,CAACR,YAAY;cAACiE,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CACpC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACf,EAAA,CAxSIZ,eAAe;AAAAoE,EAAA,GAAfpE,eAAe;AA0SrB,eAAeA,eAAe;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}