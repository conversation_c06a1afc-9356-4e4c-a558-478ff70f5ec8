import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Tb<PERSON>lock,
  TbChevronLeft,
  TbChevronRight,
  TbCheck,
  TbX,
  TbFlag,
  TbAlertCircle,
} from 'react-icons/tb';
import NewQuiz<PERSON>enderer from '../NewQuizRenderer';

const QuizInterface = ({
  quiz,
  questions = [],
  onSubmit,
  onExit,
  className = ''
}) => {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [timeRemaining, setTimeRemaining] = useState((quiz?.duration || 30) * 60); // Convert to seconds

  const currentQuestion = questions[currentQuestionIndex];
  const totalQuestions = questions.length;

  // Timer effect
  useEffect(() => {
    if (timeRemaining <= 0) {
      handleSubmit();
      return;
    }

    const timer = setInterval(() => {
      setTimeRemaining(prev => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeRemaining]);

  // Handle answer selection
  const handleAnswerChange = (answer) => {
    setAnswers(prev => ({
      ...prev,
      [currentQuestionIndex]: answer
    }));
  };

  // Navigation functions
  const goToNext = () => {
    if (currentQuestionIndex < totalQuestions - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const goToPrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  // Submit quiz
  const handleSubmit = () => {
    onSubmit && onSubmit(answers);
  };

  if (!questions || questions.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">📝</div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">No Questions Available</h2>
          <p className="text-gray-600 mb-4">This quiz doesn't have any questions yet.</p>
          <button
            onClick={onExit}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <NewQuizRenderer
      question={currentQuestion}
      questionIndex={currentQuestionIndex}
      totalQuestions={totalQuestions}
      selectedAnswer={answers[currentQuestionIndex]}
      selectedOptions={answers}
      onAnswerChange={handleAnswerChange}
      timeLeft={timeRemaining}
      examTitle={quiz?.name || "Quiz"}
      isTimeWarning={timeRemaining <= 60}
      onNext={goToNext}
      onPrevious={goToPrevious}
      onSubmit={handleSubmit}
    />
  );
};
export default QuizInterface;
