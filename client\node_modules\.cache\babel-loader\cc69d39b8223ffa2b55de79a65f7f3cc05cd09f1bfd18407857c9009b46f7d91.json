{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\pages\\\\user\\\\Quiz\\\\QuizPlay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { chatWithChatGPTToGetAns } from '../../../apicalls/chat';\nimport NewQuizRenderer from '../../../components/NewQuizRenderer';\nimport QuizErrorBoundary from '../../../components/QuizErrorBoundary';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuizPlay = () => {\n  _s();\n  var _questions$selectedQu;\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const [startTime, setStartTime] = useState(null);\n  const [forceUpdate, setForceUpdate] = useState(0);\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const {\n    user\n  } = useSelector(state => state.user);\n  const getExamData = async (retryCount = 0) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getExamById({\n        examId: id\n      });\n      dispatch(HideLoading());\n      if (response.success) {\n        var _response$data, _response$data2, _response$data3;\n        const questionsArray = ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.questions) || [];\n        console.log('🔍 Quiz Data Debug:', {\n          examId: id,\n          examName: (_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.name,\n          totalQuestions: questionsArray.length,\n          firstQuestion: questionsArray[0],\n          questionStructure: questionsArray.map(q => ({\n            id: q === null || q === void 0 ? void 0 : q._id,\n            name: q === null || q === void 0 ? void 0 : q.name,\n            question: q === null || q === void 0 ? void 0 : q.question,\n            type: q === null || q === void 0 ? void 0 : q.type,\n            answerType: q === null || q === void 0 ? void 0 : q.answerType,\n            hasOptions: q !== null && q !== void 0 && q.options ? Object.keys(q.options).length : 0\n          }))\n        });\n\n        // Check if questions are properly populated\n        if (questionsArray.length === 0) {\n          console.warn('No questions found for this quiz');\n          setQuestions([]);\n          setExamData(response.data);\n          return;\n        }\n\n        // Validate question structure with more detailed checking\n        const validQuestions = questionsArray.filter(q => {\n          if (!q) return false;\n\n          // Check if question has text\n          const hasQuestionText = q.name || q.question || q.text;\n          if (!hasQuestionText) {\n            console.warn('Question missing text:', q);\n            return false;\n          }\n\n          // For MCQ questions, check if they have options\n          if ((q.type === 'mcq' || q.answerType === 'Options') && (!q.options || Object.keys(q.options).length === 0)) {\n            console.warn('MCQ question missing options:', q);\n            return false;\n          }\n          return true;\n        });\n        console.log('✅ Valid questions found:', validQuestions.length);\n        if (validQuestions.length === 0) {\n          console.warn('No valid questions found for this quiz');\n          setQuestions([]);\n          setExamData(response.data);\n          return;\n        }\n\n        // Set data immediately for instant rendering\n        setExamData(response.data);\n        setQuestions(validQuestions);\n        setSecondsLeft((((_response$data3 = response.data) === null || _response$data3 === void 0 ? void 0 : _response$data3.duration) || 0) * 60);\n\n        // Force immediate re-render\n        setForceUpdate(prev => prev + 1);\n      } else {\n        message.error(response.message);\n        navigate('/user/quiz');\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n\n      // Retry logic for network errors\n      if (retryCount < 2 && (error.code === 'ECONNABORTED' || !error.response)) {\n        console.log(`Retrying quiz data fetch... Attempt ${retryCount + 1}`);\n        setTimeout(() => getExamData(retryCount + 1), 1000);\n        return;\n      }\n      message.error(error.message || 'Failed to load quiz. Please try again.');\n      navigate('/user/quiz');\n    }\n  };\n  const checkFreeTextAnswers = async payload => {\n    if (!payload.length) return [];\n    const {\n      data\n    } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n  const calculateResult = useCallback(async () => {\n    try {\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n      dispatch(ShowLoading());\n      const freeTextPayload = [];\n      questions.forEach((q, idx) => {\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\"\n          });\n        }\n      });\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n      gptResults.forEach(r => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = {\n            isCorrect: r.isCorrect,\n            reason: r.reason || \"\"\n          };\n        }\n      });\n      const correctAnswers = [];\n      const wrongAnswers = [];\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const {\n            isCorrect = false,\n            reason = \"\"\n          } = gptMap[q.name] || {};\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey,\n            reason\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        } else if (q.type === \"mcq\" || q.answerType === \"Options\") {\n          const correctKey = q.correctOption || q.correctAnswer;\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = {\n            ...q,\n            userAnswer: userAnswerKey\n          };\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        }\n      });\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\n      const totalTimeAllowed = ((examData === null || examData === void 0 ? void 0 : examData.duration) || 0) * 60;\n      const totalQuestions = questions.length;\n      const correctCount = correctAnswers.length;\n      const scorePercentage = Math.round(correctCount / totalQuestions * 100);\n      const points = correctCount * 10;\n\n      // Handle both passingMarks and passingPercentage for backward compatibility\n      const passingPercentage = examData.passingPercentage || examData.passingMarks || 70;\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\n      const tempResult = {\n        correctAnswers,\n        wrongAnswers,\n        verdict,\n        score: scorePercentage,\n        points: points,\n        totalQuestions: totalQuestions,\n        timeSpent: timeSpent,\n        totalTimeAllowed: totalTimeAllowed\n      };\n      const response = await addReport({\n        exam: id,\n        result: tempResult,\n        user: user._id\n      });\n      if (response.success) {\n        var _response$xpData, _response$xpData2, _response$xpData3, _response$xpData4;\n        // Clear ranking cache for real-time updates\n        localStorage.removeItem('rankingCache');\n        localStorage.removeItem('userRankingPosition');\n        localStorage.removeItem('leaderboardData');\n\n        // Trigger ranking update event for real-time updates\n        window.dispatchEvent(new CustomEvent('rankingUpdate', {\n          detail: {\n            userId: user._id,\n            xpGained: ((_response$xpData = response.xpData) === null || _response$xpData === void 0 ? void 0 : _response$xpData.xpAwarded) || 0,\n            newTotalXP: ((_response$xpData2 = response.xpData) === null || _response$xpData2 === void 0 ? void 0 : _response$xpData2.newTotalXP) || 0,\n            levelUp: ((_response$xpData3 = response.xpData) === null || _response$xpData3 === void 0 ? void 0 : _response$xpData3.levelUp) || false,\n            newLevel: ((_response$xpData4 = response.xpData) === null || _response$xpData4 === void 0 ? void 0 : _response$xpData4.newLevel) || user.currentLevel\n          }\n        }));\n\n        // Debug XP data\n        console.log('🔍 Quiz completion response:', response);\n        console.log('💰 XP Data received:', response.xpData);\n        const resultWithXP = {\n          ...tempResult,\n          xpData: response.xpData\n        };\n        console.log('📊 Final result with XP:', resultWithXP);\n        navigate(`/quiz/${id}/result`, {\n          state: {\n            result: resultWithXP\n          }\n        });\n      } else {\n        message.error(response.message);\n        console.error('❌ Quiz submission failed:', response.message);\n      }\n      dispatch(HideLoading());\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);\n  const startTimer = useCallback(() => {\n    const totalSeconds = ((examData === null || examData === void 0 ? void 0 : examData.duration) || 0) * 60;\n    setSecondsLeft(totalSeconds);\n    setStartTime(Date.now());\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft(prevSeconds => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  }, [examData]);\n  useEffect(() => {\n    if (timeUp && intervalId) {\n      clearInterval(intervalId);\n      setIntervalId(null);\n      calculateResult();\n    }\n  }, [timeUp, intervalId, calculateResult]);\n  useEffect(() => {\n    if (id) {\n      // Start loading immediately without delay\n      getExamData();\n    }\n  }, []);\n\n  // Force update when questions are loaded\n  useEffect(() => {\n    if (questions.length > 0) {\n      console.log('🔄 Questions loaded, forcing update...');\n      setForceUpdate(prev => prev + 1);\n    }\n  }, [questions.length]);\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n  useEffect(() => {\n    if (examData && questions.length > 0) {\n      startTimer();\n    }\n  }, [examData, questions]);\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n        setIntervalId(null);\n      }\n    };\n  }, [intervalId]);\n\n  // NEVER show loading screen - always render quiz interface\n  // Create fallback exam data if needed\n  const safeExamData = examData || {\n    _id: id,\n    name: 'Loading Quiz...',\n    duration: 30,\n    questions: []\n  };\n\n  // Create fallback questions if needed\n  const safeQuestions = questions.length > 0 ? questions : [{\n    _id: 'loading-1',\n    name: 'Loading question...',\n    question: 'Please wait while questions load...',\n    type: 'mcq',\n    options: {\n      A: 'Loading...',\n      B: 'Please wait...',\n      C: 'Almost ready...',\n      D: 'Loading...'\n    }\n  }];\n\n  // Handle case where selectedQuestionIndex is out of bounds\n  if (safeQuestions.length > 0 && selectedQuestionIndex >= safeQuestions.length) {\n    setSelectedQuestionIndex(0);\n  }\n\n  // Always use safe questions - never show loading\n  const currentQuestion = safeQuestions[selectedQuestionIndex] || safeQuestions[0];\n\n  // Final debug before rendering\n  console.log('🎯 Final QuizPlay render state:', {\n    examData: examData ? {\n      name: examData.name,\n      id: examData._id\n    } : null,\n    questionsCount: questions.length,\n    selectedQuestionIndex,\n    currentQuestion: questions[selectedQuestionIndex] ? {\n      id: questions[selectedQuestionIndex]._id,\n      name: ((_questions$selectedQu = questions[selectedQuestionIndex].name) === null || _questions$selectedQu === void 0 ? void 0 : _questions$selectedQu.substring(0, 50)) + '...',\n      type: questions[selectedQuestionIndex].type,\n      hasOptions: questions[selectedQuestionIndex].options ? Object.keys(questions[selectedQuestionIndex].options).length : 0\n    } : null,\n    timeLeft: secondsLeft\n  });\n\n  // Always use safe data - never show loading\n  const activeQuestion = currentQuestion;\n  const mountKey = `quiz-${safeExamData._id}-${selectedQuestionIndex}-${forceUpdate}-${safeQuestions.length}`;\n  return /*#__PURE__*/_jsxDEV(QuizErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(NewQuizRenderer, {\n      question: activeQuestion,\n      questionIndex: selectedQuestionIndex,\n      totalQuestions: safeQuestions.length,\n      selectedAnswer: selectedOptions[selectedQuestionIndex],\n      selectedOptions: selectedOptions // Pass all selected options for navigation indicators\n      ,\n      onAnswerChange: answer => {\n        console.log('💾 Saving answer for question', selectedQuestionIndex + 1, ':', answer);\n        const newSelectedOptions = {\n          ...selectedOptions,\n          [selectedQuestionIndex]: answer\n        };\n        setSelectedOptions(newSelectedOptions);\n        console.log('📋 All answers so far:', newSelectedOptions);\n      },\n      timeLeft: secondsLeft,\n      examTitle: safeExamData.name || \"Quiz\",\n      isTimeWarning: secondsLeft <= 60,\n      onNext: () => {\n        console.log('➡️ Next button clicked, current index:', selectedQuestionIndex, 'total:', safeQuestions.length);\n        if (selectedQuestionIndex === safeQuestions.length - 1) {\n          console.log('🏁 Last question reached, calculating result...');\n          calculateResult();\n        } else {\n          console.log('📝 Moving to next question:', selectedQuestionIndex + 1);\n          setSelectedQuestionIndex(selectedQuestionIndex + 1);\n        }\n      },\n      onPrevious: () => {\n        console.log('🔙 Previous button clicked, current index:', selectedQuestionIndex);\n        if (selectedQuestionIndex > 0) {\n          setSelectedQuestionIndex(selectedQuestionIndex - 1);\n        }\n      },\n      onSubmit: () => {\n        console.log('🏁 Submit button clicked, calculating result...');\n        calculateResult();\n      }\n    }, mountKey, false, {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }, this)\n  }, mountKey, false, {\n    fileName: _jsxFileName,\n    lineNumber: 361,\n    columnNumber: 5\n  }, this);\n};\n_s(QuizPlay, \"UpuqtjONhY3bhnSwak3om9EbzXg=\", false, function () {\n  return [useParams, useNavigate, useDispatch, useSelector];\n});\n_c = QuizPlay;\nexport default QuizPlay;\nvar _c;\n$RefreshReg$(_c, \"QuizPlay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useParams", "useNavigate", "useDispatch", "useSelector", "message", "getExamById", "addReport", "HideLoading", "ShowLoading", "chatWithChatGPTToGetAns", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "QuizError<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "QuizPlay", "_s", "_questions$selectedQu", "examData", "setExamData", "questions", "setQuestions", "selectedQuestionIndex", "setSelectedQuestionIndex", "selectedOptions", "setSelectedOptions", "secondsLeft", "setSecondsLeft", "timeUp", "setTimeUp", "intervalId", "setIntervalId", "startTime", "setStartTime", "forceUpdate", "setForceUpdate", "id", "navigate", "dispatch", "user", "state", "getExamData", "retryCount", "response", "examId", "success", "_response$data", "_response$data2", "_response$data3", "questionsArray", "data", "console", "log", "examName", "name", "totalQuestions", "length", "firstQuestion", "questionStructure", "map", "q", "_id", "question", "type", "answerType", "hasOptions", "options", "Object", "keys", "warn", "validQuestions", "filter", "hasQuestionText", "text", "duration", "prev", "error", "code", "setTimeout", "checkFreeTextAnswers", "payload", "calculateResult", "freeTextPayload", "for<PERSON>ach", "idx", "push", "expectedAnswer", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "userAnswer", "gptResults", "gptMap", "r", "result", "isCorrect", "reason", "correctAnswers", "wrongAnswers", "userAnswerKey", "enriched", "<PERSON><PERSON><PERSON>", "timeSpent", "Math", "floor", "Date", "now", "totalTimeAllowed", "correctCount", "scorePercentage", "round", "points", "passingPercentage", "passingMarks", "verdict", "tempResult", "score", "exam", "_response$xpData", "_response$xpData2", "_response$xpData3", "_response$xpData4", "localStorage", "removeItem", "window", "dispatchEvent", "CustomEvent", "detail", "userId", "xpGained", "xpData", "xpAwarded", "newTotalXP", "levelUp", "newLevel", "currentLevel", "resultWithXP", "startTimer", "totalSeconds", "newIntervalId", "setInterval", "prevSeconds", "clearInterval", "document", "body", "classList", "add", "remove", "safeExamData", "safeQuestions", "A", "B", "C", "D", "currentQuestion", "questionsCount", "substring", "timeLeft", "activeQuestion", "<PERSON><PERSON><PERSON>", "children", "questionIndex", "<PERSON><PERSON><PERSON><PERSON>", "onAnswerChange", "answer", "newSelectedOptions", "examTitle", "isTimeWarning", "onNext", "onPrevious", "onSubmit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/pages/user/Quiz/QuizPlay.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { message } from 'antd';\nimport { getExamById } from '../../../apicalls/exams';\nimport { addReport } from '../../../apicalls/reports';\nimport { HideLoading, ShowLoading } from '../../../redux/loaderSlice';\nimport { chatWithChatGPTToGetAns } from '../../../apicalls/chat';\nimport NewQuizRenderer from '../../../components/NewQuizRenderer';\nimport QuizErrorBoundary from '../../../components/QuizErrorBoundary';\n\nconst QuizPlay = () => {\n  const [examData, setExamData] = useState(null);\n  const [questions, setQuestions] = useState([]);\n  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);\n  const [selectedOptions, setSelectedOptions] = useState({});\n  const [secondsLeft, setSecondsLeft] = useState(0);\n  const [timeUp, setTimeUp] = useState(false);\n  const [intervalId, setIntervalId] = useState(null);\n  const [startTime, setStartTime] = useState(null);\n  const [forceUpdate, setForceUpdate] = useState(0);\n\n  const { id } = useParams();\n  const navigate = useNavigate();\n  const dispatch = useDispatch();\n  const { user } = useSelector((state) => state.user);\n\n  const getExamData = async (retryCount = 0) => {\n    try {\n      dispatch(ShowLoading());\n      const response = await getExamById({ examId: id });\n      dispatch(HideLoading());\n\n      if (response.success) {\n        const questionsArray = response.data?.questions || [];\n\n        console.log('🔍 Quiz Data Debug:', {\n          examId: id,\n          examName: response.data?.name,\n          totalQuestions: questionsArray.length,\n          firstQuestion: questionsArray[0],\n          questionStructure: questionsArray.map(q => ({\n            id: q?._id,\n            name: q?.name,\n            question: q?.question,\n            type: q?.type,\n            answerType: q?.answerType,\n            hasOptions: q?.options ? Object.keys(q.options).length : 0\n          }))\n        });\n\n        // Check if questions are properly populated\n        if (questionsArray.length === 0) {\n          console.warn('No questions found for this quiz');\n          setQuestions([]);\n          setExamData(response.data);\n          return;\n        }\n\n        // Validate question structure with more detailed checking\n        const validQuestions = questionsArray.filter(q => {\n          if (!q) return false;\n\n          // Check if question has text\n          const hasQuestionText = q.name || q.question || q.text;\n          if (!hasQuestionText) {\n            console.warn('Question missing text:', q);\n            return false;\n          }\n\n          // For MCQ questions, check if they have options\n          if ((q.type === 'mcq' || q.answerType === 'Options') && (!q.options || Object.keys(q.options).length === 0)) {\n            console.warn('MCQ question missing options:', q);\n            return false;\n          }\n\n          return true;\n        });\n\n        console.log('✅ Valid questions found:', validQuestions.length);\n\n        if (validQuestions.length === 0) {\n          console.warn('No valid questions found for this quiz');\n          setQuestions([]);\n          setExamData(response.data);\n          return;\n        }\n\n        // Set data immediately for instant rendering\n        setExamData(response.data);\n        setQuestions(validQuestions);\n        setSecondsLeft((response.data?.duration || 0) * 60);\n\n        // Force immediate re-render\n        setForceUpdate(prev => prev + 1);\n      } else {\n        message.error(response.message);\n        navigate('/user/quiz');\n      }\n    } catch (error) {\n      dispatch(HideLoading());\n\n      // Retry logic for network errors\n      if (retryCount < 2 && (error.code === 'ECONNABORTED' || !error.response)) {\n        console.log(`Retrying quiz data fetch... Attempt ${retryCount + 1}`);\n        setTimeout(() => getExamData(retryCount + 1), 1000);\n        return;\n      }\n\n      message.error(error.message || 'Failed to load quiz. Please try again.');\n      navigate('/user/quiz');\n    }\n  };\n\n  const checkFreeTextAnswers = async (payload) => {\n    if (!payload.length) return [];\n    const { data } = await chatWithChatGPTToGetAns(payload);\n    return data;\n  };\n\n  const calculateResult = useCallback(async () => {\n    try {\n      if (!user || !user._id) {\n        message.error(\"User not found. Please log in again.\");\n        navigate(\"/login\");\n        return;\n      }\n\n      dispatch(ShowLoading());\n\n      const freeTextPayload = [];\n      questions.forEach((q, idx) => {\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          freeTextPayload.push({\n            question: q.name,\n            expectedAnswer: q.correctAnswer || q.correctOption,\n            userAnswer: selectedOptions[idx] || \"\",\n          });\n        }\n      });\n\n      const gptResults = await checkFreeTextAnswers(freeTextPayload);\n      const gptMap = {};\n\n      gptResults.forEach((r) => {\n        if (r.result && typeof r.result.isCorrect === \"boolean\") {\n          gptMap[r.question] = r.result;\n        } else if (typeof r.isCorrect === \"boolean\") {\n          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || \"\" };\n        }\n      });\n\n      const correctAnswers = [];\n      const wrongAnswers = [];\n\n      questions.forEach((q, idx) => {\n        const userAnswerKey = selectedOptions[idx] || \"\";\n\n        if (q.type === \"fill\" || q.answerType === \"Free Text\" || q.answerType === \"Fill in the Blank\") {\n          const { isCorrect = false, reason = \"\" } = gptMap[q.name] || {};\n          const enriched = { ...q, userAnswer: userAnswerKey, reason };\n\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        } else if (q.type === \"mcq\" || q.answerType === \"Options\") {\n          const correctKey = q.correctOption || q.correctAnswer;\n          const isCorrect = correctKey === userAnswerKey;\n          const enriched = { ...q, userAnswer: userAnswerKey };\n\n          if (isCorrect) {\n            correctAnswers.push(enriched);\n          } else {\n            wrongAnswers.push(enriched);\n          }\n        }\n      });\n\n      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;\n      const totalTimeAllowed = (examData?.duration || 0) * 60;\n      const totalQuestions = questions.length;\n      const correctCount = correctAnswers.length;\n      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);\n      const points = correctCount * 10;\n\n      // Handle both passingMarks and passingPercentage for backward compatibility\n      const passingPercentage = examData.passingPercentage || examData.passingMarks || 70;\n      const verdict = scorePercentage >= passingPercentage ? \"Pass\" : \"Fail\";\n\n      const tempResult = {\n        correctAnswers,\n        wrongAnswers,\n        verdict,\n        score: scorePercentage,\n        points: points,\n        totalQuestions: totalQuestions,\n        timeSpent: timeSpent,\n        totalTimeAllowed: totalTimeAllowed\n      };\n\n      const response = await addReport({\n        exam: id,\n        result: tempResult,\n        user: user._id,\n      });\n\n      if (response.success) {\n        // Clear ranking cache for real-time updates\n        localStorage.removeItem('rankingCache');\n        localStorage.removeItem('userRankingPosition');\n        localStorage.removeItem('leaderboardData');\n\n        // Trigger ranking update event for real-time updates\n        window.dispatchEvent(new CustomEvent('rankingUpdate', {\n          detail: {\n            userId: user._id,\n            xpGained: response.xpData?.xpAwarded || 0,\n            newTotalXP: response.xpData?.newTotalXP || 0,\n            levelUp: response.xpData?.levelUp || false,\n            newLevel: response.xpData?.newLevel || user.currentLevel\n          }\n        }));\n\n        // Debug XP data\n        console.log('🔍 Quiz completion response:', response);\n        console.log('💰 XP Data received:', response.xpData);\n\n        const resultWithXP = {\n          ...tempResult,\n          xpData: response.xpData\n        };\n\n        console.log('📊 Final result with XP:', resultWithXP);\n        navigate(`/quiz/${id}/result`, { state: { result: resultWithXP } });\n      } else {\n        message.error(response.message);\n        console.error('❌ Quiz submission failed:', response.message);\n      }\n      dispatch(HideLoading());\n\n    } catch (error) {\n      dispatch(HideLoading());\n      message.error(error.message);\n    }\n  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);\n\n  const startTimer = useCallback(() => {\n    const totalSeconds = (examData?.duration || 0) * 60;\n    setSecondsLeft(totalSeconds);\n    setStartTime(Date.now());\n\n    const newIntervalId = setInterval(() => {\n      setSecondsLeft((prevSeconds) => {\n        if (prevSeconds > 0) {\n          return prevSeconds - 1;\n        } else {\n          setTimeUp(true);\n          return 0;\n        }\n      });\n    }, 1000);\n    setIntervalId(newIntervalId);\n  }, [examData]);\n\n  useEffect(() => {\n    if (timeUp && intervalId) {\n      clearInterval(intervalId);\n      setIntervalId(null);\n      calculateResult();\n    }\n  }, [timeUp, intervalId, calculateResult]);\n\n  useEffect(() => {\n    if (id) {\n      // Start loading immediately without delay\n      getExamData();\n    }\n  }, []);\n\n  // Force update when questions are loaded\n  useEffect(() => {\n    if (questions.length > 0) {\n      console.log('🔄 Questions loaded, forcing update...');\n      setForceUpdate(prev => prev + 1);\n    }\n  }, [questions.length]);\n\n  useEffect(() => {\n    document.body.classList.add('quiz-fullscreen');\n    return () => {\n      document.body.classList.remove('quiz-fullscreen');\n    };\n  }, []);\n\n  useEffect(() => {\n    if (examData && questions.length > 0) {\n      startTimer();\n    }\n  }, [examData, questions]);\n\n  useEffect(() => {\n    return () => {\n      if (intervalId) {\n        clearInterval(intervalId);\n        setIntervalId(null);\n      }\n    };\n  }, [intervalId]);\n\n  // NEVER show loading screen - always render quiz interface\n  // Create fallback exam data if needed\n  const safeExamData = examData || {\n    _id: id,\n    name: 'Loading Quiz...',\n    duration: 30,\n    questions: []\n  };\n\n  // Create fallback questions if needed\n  const safeQuestions = questions.length > 0 ? questions : [\n    {\n      _id: 'loading-1',\n      name: 'Loading question...',\n      question: 'Please wait while questions load...',\n      type: 'mcq',\n      options: { A: 'Loading...', B: 'Please wait...', C: 'Almost ready...', D: 'Loading...' }\n    }\n  ];\n\n\n\n  // Handle case where selectedQuestionIndex is out of bounds\n  if (safeQuestions.length > 0 && selectedQuestionIndex >= safeQuestions.length) {\n    setSelectedQuestionIndex(0);\n  }\n\n  // Always use safe questions - never show loading\n  const currentQuestion = safeQuestions[selectedQuestionIndex] || safeQuestions[0];\n\n  // Final debug before rendering\n  console.log('🎯 Final QuizPlay render state:', {\n    examData: examData ? { name: examData.name, id: examData._id } : null,\n    questionsCount: questions.length,\n    selectedQuestionIndex,\n    currentQuestion: questions[selectedQuestionIndex] ? {\n      id: questions[selectedQuestionIndex]._id,\n      name: questions[selectedQuestionIndex].name?.substring(0, 50) + '...',\n      type: questions[selectedQuestionIndex].type,\n      hasOptions: questions[selectedQuestionIndex].options ? Object.keys(questions[selectedQuestionIndex].options).length : 0\n    } : null,\n    timeLeft: secondsLeft\n  });\n\n  // Always use safe data - never show loading\n  const activeQuestion = currentQuestion;\n  const mountKey = `quiz-${safeExamData._id}-${selectedQuestionIndex}-${forceUpdate}-${safeQuestions.length}`;\n\n  return (\n    <QuizErrorBoundary key={mountKey}>\n      <NewQuizRenderer\n        key={mountKey}\n        question={activeQuestion}\n        questionIndex={selectedQuestionIndex}\n        totalQuestions={safeQuestions.length}\n        selectedAnswer={selectedOptions[selectedQuestionIndex]}\n        selectedOptions={selectedOptions} // Pass all selected options for navigation indicators\n        onAnswerChange={(answer) => {\n          console.log('💾 Saving answer for question', selectedQuestionIndex + 1, ':', answer);\n          const newSelectedOptions = {\n            ...selectedOptions,\n            [selectedQuestionIndex]: answer,\n          };\n          setSelectedOptions(newSelectedOptions);\n          console.log('📋 All answers so far:', newSelectedOptions);\n        }}\n        timeLeft={secondsLeft}\n        examTitle={safeExamData.name || \"Quiz\"}\n        isTimeWarning={secondsLeft <= 60}\n        onNext={() => {\n          console.log('➡️ Next button clicked, current index:', selectedQuestionIndex, 'total:', safeQuestions.length);\n          if (selectedQuestionIndex === safeQuestions.length - 1) {\n            console.log('🏁 Last question reached, calculating result...');\n            calculateResult();\n          } else {\n            console.log('📝 Moving to next question:', selectedQuestionIndex + 1);\n            setSelectedQuestionIndex(selectedQuestionIndex + 1);\n          }\n        }}\n        onPrevious={() => {\n          console.log('🔙 Previous button clicked, current index:', selectedQuestionIndex);\n          if (selectedQuestionIndex > 0) {\n            setSelectedQuestionIndex(selectedQuestionIndex - 1);\n          }\n        }}\n        onSubmit={() => {\n          console.log('🏁 Submit button clicked, calculating result...');\n          calculateResult();\n        }}\n      />\n    </QuizErrorBoundary>\n  );\n};\n\nexport default QuizPlay;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,SAAS,QAAQ,2BAA2B;AACrD,SAASC,WAAW,EAAEC,WAAW,QAAQ,4BAA4B;AACrE,SAASC,uBAAuB,QAAQ,wBAAwB;AAChE,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,iBAAiB,MAAM,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtE,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA;EACrB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACwB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGzB,QAAQ,CAAC,CAAC,CAAC;EACrE,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAAC8B,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAC,CAAC,CAAC;EAEjD,MAAM;IAAEsC;EAAG,CAAC,GAAGnC,SAAS,CAAC,CAAC;EAC1B,MAAMoC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAMoC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEoC;EAAK,CAAC,GAAGnC,WAAW,CAAEoC,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAEnD,MAAME,WAAW,GAAG,MAAAA,CAAOC,UAAU,GAAG,CAAC,KAAK;IAC5C,IAAI;MACFJ,QAAQ,CAAC7B,WAAW,CAAC,CAAC,CAAC;MACvB,MAAMkC,QAAQ,GAAG,MAAMrC,WAAW,CAAC;QAAEsC,MAAM,EAAER;MAAG,CAAC,CAAC;MAClDE,QAAQ,CAAC9B,WAAW,CAAC,CAAC,CAAC;MAEvB,IAAImC,QAAQ,CAACE,OAAO,EAAE;QAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,eAAA;QACpB,MAAMC,cAAc,GAAG,EAAAH,cAAA,GAAAH,QAAQ,CAACO,IAAI,cAAAJ,cAAA,uBAAbA,cAAA,CAAe1B,SAAS,KAAI,EAAE;QAErD+B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;UACjCR,MAAM,EAAER,EAAE;UACViB,QAAQ,GAAAN,eAAA,GAAEJ,QAAQ,CAACO,IAAI,cAAAH,eAAA,uBAAbA,eAAA,CAAeO,IAAI;UAC7BC,cAAc,EAAEN,cAAc,CAACO,MAAM;UACrCC,aAAa,EAAER,cAAc,CAAC,CAAC,CAAC;UAChCS,iBAAiB,EAAET,cAAc,CAACU,GAAG,CAACC,CAAC,KAAK;YAC1CxB,EAAE,EAAEwB,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEC,GAAG;YACVP,IAAI,EAAEM,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEN,IAAI;YACbQ,QAAQ,EAAEF,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEE,QAAQ;YACrBC,IAAI,EAAEH,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEG,IAAI;YACbC,UAAU,EAAEJ,CAAC,aAADA,CAAC,uBAADA,CAAC,CAAEI,UAAU;YACzBC,UAAU,EAAEL,CAAC,aAADA,CAAC,eAADA,CAAC,CAAEM,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACR,CAAC,CAACM,OAAO,CAAC,CAACV,MAAM,GAAG;UAC3D,CAAC,CAAC;QACJ,CAAC,CAAC;;QAEF;QACA,IAAIP,cAAc,CAACO,MAAM,KAAK,CAAC,EAAE;UAC/BL,OAAO,CAACkB,IAAI,CAAC,kCAAkC,CAAC;UAChDhD,YAAY,CAAC,EAAE,CAAC;UAChBF,WAAW,CAACwB,QAAQ,CAACO,IAAI,CAAC;UAC1B;QACF;;QAEA;QACA,MAAMoB,cAAc,GAAGrB,cAAc,CAACsB,MAAM,CAACX,CAAC,IAAI;UAChD,IAAI,CAACA,CAAC,EAAE,OAAO,KAAK;;UAEpB;UACA,MAAMY,eAAe,GAAGZ,CAAC,CAACN,IAAI,IAAIM,CAAC,CAACE,QAAQ,IAAIF,CAAC,CAACa,IAAI;UACtD,IAAI,CAACD,eAAe,EAAE;YACpBrB,OAAO,CAACkB,IAAI,CAAC,wBAAwB,EAAET,CAAC,CAAC;YACzC,OAAO,KAAK;UACd;;UAEA;UACA,IAAI,CAACA,CAAC,CAACG,IAAI,KAAK,KAAK,IAAIH,CAAC,CAACI,UAAU,KAAK,SAAS,MAAM,CAACJ,CAAC,CAACM,OAAO,IAAIC,MAAM,CAACC,IAAI,CAACR,CAAC,CAACM,OAAO,CAAC,CAACV,MAAM,KAAK,CAAC,CAAC,EAAE;YAC3GL,OAAO,CAACkB,IAAI,CAAC,+BAA+B,EAAET,CAAC,CAAC;YAChD,OAAO,KAAK;UACd;UAEA,OAAO,IAAI;QACb,CAAC,CAAC;QAEFT,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEkB,cAAc,CAACd,MAAM,CAAC;QAE9D,IAAIc,cAAc,CAACd,MAAM,KAAK,CAAC,EAAE;UAC/BL,OAAO,CAACkB,IAAI,CAAC,wCAAwC,CAAC;UACtDhD,YAAY,CAAC,EAAE,CAAC;UAChBF,WAAW,CAACwB,QAAQ,CAACO,IAAI,CAAC;UAC1B;QACF;;QAEA;QACA/B,WAAW,CAACwB,QAAQ,CAACO,IAAI,CAAC;QAC1B7B,YAAY,CAACiD,cAAc,CAAC;QAC5B3C,cAAc,CAAC,CAAC,EAAAqB,eAAA,GAAAL,QAAQ,CAACO,IAAI,cAAAF,eAAA,uBAAbA,eAAA,CAAe0B,QAAQ,KAAI,CAAC,IAAI,EAAE,CAAC;;QAEnD;QACAvC,cAAc,CAACwC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MAClC,CAAC,MAAM;QACLtE,OAAO,CAACuE,KAAK,CAACjC,QAAQ,CAACtC,OAAO,CAAC;QAC/BgC,QAAQ,CAAC,YAAY,CAAC;MACxB;IACF,CAAC,CAAC,OAAOuC,KAAK,EAAE;MACdtC,QAAQ,CAAC9B,WAAW,CAAC,CAAC,CAAC;;MAEvB;MACA,IAAIkC,UAAU,GAAG,CAAC,KAAKkC,KAAK,CAACC,IAAI,KAAK,cAAc,IAAI,CAACD,KAAK,CAACjC,QAAQ,CAAC,EAAE;QACxEQ,OAAO,CAACC,GAAG,CAAE,uCAAsCV,UAAU,GAAG,CAAE,EAAC,CAAC;QACpEoC,UAAU,CAAC,MAAMrC,WAAW,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC;QACnD;MACF;MAEArC,OAAO,CAACuE,KAAK,CAACA,KAAK,CAACvE,OAAO,IAAI,wCAAwC,CAAC;MACxEgC,QAAQ,CAAC,YAAY,CAAC;IACxB;EACF,CAAC;EAED,MAAM0C,oBAAoB,GAAG,MAAOC,OAAO,IAAK;IAC9C,IAAI,CAACA,OAAO,CAACxB,MAAM,EAAE,OAAO,EAAE;IAC9B,MAAM;MAAEN;IAAK,CAAC,GAAG,MAAMxC,uBAAuB,CAACsE,OAAO,CAAC;IACvD,OAAO9B,IAAI;EACb,CAAC;EAED,MAAM+B,eAAe,GAAGjF,WAAW,CAAC,YAAY;IAC9C,IAAI;MACF,IAAI,CAACuC,IAAI,IAAI,CAACA,IAAI,CAACsB,GAAG,EAAE;QACtBxD,OAAO,CAACuE,KAAK,CAAC,sCAAsC,CAAC;QACrDvC,QAAQ,CAAC,QAAQ,CAAC;QAClB;MACF;MAEAC,QAAQ,CAAC7B,WAAW,CAAC,CAAC,CAAC;MAEvB,MAAMyE,eAAe,GAAG,EAAE;MAC1B9D,SAAS,CAAC+D,OAAO,CAAC,CAACvB,CAAC,EAAEwB,GAAG,KAAK;QAC5B,IAAIxB,CAAC,CAACG,IAAI,KAAK,MAAM,IAAIH,CAAC,CAACI,UAAU,KAAK,WAAW,IAAIJ,CAAC,CAACI,UAAU,KAAK,mBAAmB,EAAE;UAC7FkB,eAAe,CAACG,IAAI,CAAC;YACnBvB,QAAQ,EAAEF,CAAC,CAACN,IAAI;YAChBgC,cAAc,EAAE1B,CAAC,CAAC2B,aAAa,IAAI3B,CAAC,CAAC4B,aAAa;YAClDC,UAAU,EAAEjE,eAAe,CAAC4D,GAAG,CAAC,IAAI;UACtC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,MAAMM,UAAU,GAAG,MAAMX,oBAAoB,CAACG,eAAe,CAAC;MAC9D,MAAMS,MAAM,GAAG,CAAC,CAAC;MAEjBD,UAAU,CAACP,OAAO,CAAES,CAAC,IAAK;QACxB,IAAIA,CAAC,CAACC,MAAM,IAAI,OAAOD,CAAC,CAACC,MAAM,CAACC,SAAS,KAAK,SAAS,EAAE;UACvDH,MAAM,CAACC,CAAC,CAAC9B,QAAQ,CAAC,GAAG8B,CAAC,CAACC,MAAM;QAC/B,CAAC,MAAM,IAAI,OAAOD,CAAC,CAACE,SAAS,KAAK,SAAS,EAAE;UAC3CH,MAAM,CAACC,CAAC,CAAC9B,QAAQ,CAAC,GAAG;YAAEgC,SAAS,EAAEF,CAAC,CAACE,SAAS;YAAEC,MAAM,EAAEH,CAAC,CAACG,MAAM,IAAI;UAAG,CAAC;QACzE;MACF,CAAC,CAAC;MAEF,MAAMC,cAAc,GAAG,EAAE;MACzB,MAAMC,YAAY,GAAG,EAAE;MAEvB7E,SAAS,CAAC+D,OAAO,CAAC,CAACvB,CAAC,EAAEwB,GAAG,KAAK;QAC5B,MAAMc,aAAa,GAAG1E,eAAe,CAAC4D,GAAG,CAAC,IAAI,EAAE;QAEhD,IAAIxB,CAAC,CAACG,IAAI,KAAK,MAAM,IAAIH,CAAC,CAACI,UAAU,KAAK,WAAW,IAAIJ,CAAC,CAACI,UAAU,KAAK,mBAAmB,EAAE;UAC7F,MAAM;YAAE8B,SAAS,GAAG,KAAK;YAAEC,MAAM,GAAG;UAAG,CAAC,GAAGJ,MAAM,CAAC/B,CAAC,CAACN,IAAI,CAAC,IAAI,CAAC,CAAC;UAC/D,MAAM6C,QAAQ,GAAG;YAAE,GAAGvC,CAAC;YAAE6B,UAAU,EAAES,aAAa;YAAEH;UAAO,CAAC;UAE5D,IAAID,SAAS,EAAE;YACbE,cAAc,CAACX,IAAI,CAACc,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLF,YAAY,CAACZ,IAAI,CAACc,QAAQ,CAAC;UAC7B;QACF,CAAC,MAAM,IAAIvC,CAAC,CAACG,IAAI,KAAK,KAAK,IAAIH,CAAC,CAACI,UAAU,KAAK,SAAS,EAAE;UACzD,MAAMoC,UAAU,GAAGxC,CAAC,CAAC4B,aAAa,IAAI5B,CAAC,CAAC2B,aAAa;UACrD,MAAMO,SAAS,GAAGM,UAAU,KAAKF,aAAa;UAC9C,MAAMC,QAAQ,GAAG;YAAE,GAAGvC,CAAC;YAAE6B,UAAU,EAAES;UAAc,CAAC;UAEpD,IAAIJ,SAAS,EAAE;YACbE,cAAc,CAACX,IAAI,CAACc,QAAQ,CAAC;UAC/B,CAAC,MAAM;YACLF,YAAY,CAACZ,IAAI,CAACc,QAAQ,CAAC;UAC7B;QACF;MACF,CAAC,CAAC;MAEF,MAAME,SAAS,GAAGrE,SAAS,GAAGsE,IAAI,CAACC,KAAK,CAAC,CAACC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGzE,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC;MAC7E,MAAM0E,gBAAgB,GAAG,CAAC,CAAAxF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwD,QAAQ,KAAI,CAAC,IAAI,EAAE;MACvD,MAAMnB,cAAc,GAAGnC,SAAS,CAACoC,MAAM;MACvC,MAAMmD,YAAY,GAAGX,cAAc,CAACxC,MAAM;MAC1C,MAAMoD,eAAe,GAAGN,IAAI,CAACO,KAAK,CAAEF,YAAY,GAAGpD,cAAc,GAAI,GAAG,CAAC;MACzE,MAAMuD,MAAM,GAAGH,YAAY,GAAG,EAAE;;MAEhC;MACA,MAAMI,iBAAiB,GAAG7F,QAAQ,CAAC6F,iBAAiB,IAAI7F,QAAQ,CAAC8F,YAAY,IAAI,EAAE;MACnF,MAAMC,OAAO,GAAGL,eAAe,IAAIG,iBAAiB,GAAG,MAAM,GAAG,MAAM;MAEtE,MAAMG,UAAU,GAAG;QACjBlB,cAAc;QACdC,YAAY;QACZgB,OAAO;QACPE,KAAK,EAAEP,eAAe;QACtBE,MAAM,EAAEA,MAAM;QACdvD,cAAc,EAAEA,cAAc;QAC9B8C,SAAS,EAAEA,SAAS;QACpBK,gBAAgB,EAAEA;MACpB,CAAC;MAED,MAAM/D,QAAQ,GAAG,MAAMpC,SAAS,CAAC;QAC/B6G,IAAI,EAAEhF,EAAE;QACRyD,MAAM,EAAEqB,UAAU;QAClB3E,IAAI,EAAEA,IAAI,CAACsB;MACb,CAAC,CAAC;MAEF,IAAIlB,QAAQ,CAACE,OAAO,EAAE;QAAA,IAAAwE,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;QACpB;QACAC,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;QACvCD,YAAY,CAACC,UAAU,CAAC,qBAAqB,CAAC;QAC9CD,YAAY,CAACC,UAAU,CAAC,iBAAiB,CAAC;;QAE1C;QACAC,MAAM,CAACC,aAAa,CAAC,IAAIC,WAAW,CAAC,eAAe,EAAE;UACpDC,MAAM,EAAE;YACNC,MAAM,EAAExF,IAAI,CAACsB,GAAG;YAChBmE,QAAQ,EAAE,EAAAX,gBAAA,GAAA1E,QAAQ,CAACsF,MAAM,cAAAZ,gBAAA,uBAAfA,gBAAA,CAAiBa,SAAS,KAAI,CAAC;YACzCC,UAAU,EAAE,EAAAb,iBAAA,GAAA3E,QAAQ,CAACsF,MAAM,cAAAX,iBAAA,uBAAfA,iBAAA,CAAiBa,UAAU,KAAI,CAAC;YAC5CC,OAAO,EAAE,EAAAb,iBAAA,GAAA5E,QAAQ,CAACsF,MAAM,cAAAV,iBAAA,uBAAfA,iBAAA,CAAiBa,OAAO,KAAI,KAAK;YAC1CC,QAAQ,EAAE,EAAAb,iBAAA,GAAA7E,QAAQ,CAACsF,MAAM,cAAAT,iBAAA,uBAAfA,iBAAA,CAAiBa,QAAQ,KAAI9F,IAAI,CAAC+F;UAC9C;QACF,CAAC,CAAC,CAAC;;QAEH;QACAnF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAET,QAAQ,CAAC;QACrDQ,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAET,QAAQ,CAACsF,MAAM,CAAC;QAEpD,MAAMM,YAAY,GAAG;UACnB,GAAGrB,UAAU;UACbe,MAAM,EAAEtF,QAAQ,CAACsF;QACnB,CAAC;QAED9E,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEmF,YAAY,CAAC;QACrDlG,QAAQ,CAAE,SAAQD,EAAG,SAAQ,EAAE;UAAEI,KAAK,EAAE;YAAEqD,MAAM,EAAE0C;UAAa;QAAE,CAAC,CAAC;MACrE,CAAC,MAAM;QACLlI,OAAO,CAACuE,KAAK,CAACjC,QAAQ,CAACtC,OAAO,CAAC;QAC/B8C,OAAO,CAACyB,KAAK,CAAC,2BAA2B,EAAEjC,QAAQ,CAACtC,OAAO,CAAC;MAC9D;MACAiC,QAAQ,CAAC9B,WAAW,CAAC,CAAC,CAAC;IAEzB,CAAC,CAAC,OAAOoE,KAAK,EAAE;MACdtC,QAAQ,CAAC9B,WAAW,CAAC,CAAC,CAAC;MACvBH,OAAO,CAACuE,KAAK,CAACA,KAAK,CAACvE,OAAO,CAAC;IAC9B;EACF,CAAC,EAAE,CAACe,SAAS,EAAEI,eAAe,EAAEN,QAAQ,EAAEkB,EAAE,EAAEG,IAAI,EAAEF,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAExE,MAAMkG,UAAU,GAAGxI,WAAW,CAAC,MAAM;IACnC,MAAMyI,YAAY,GAAG,CAAC,CAAAvH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEwD,QAAQ,KAAI,CAAC,IAAI,EAAE;IACnD/C,cAAc,CAAC8G,YAAY,CAAC;IAC5BxG,YAAY,CAACuE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IAExB,MAAMiC,aAAa,GAAGC,WAAW,CAAC,MAAM;MACtChH,cAAc,CAAEiH,WAAW,IAAK;QAC9B,IAAIA,WAAW,GAAG,CAAC,EAAE;UACnB,OAAOA,WAAW,GAAG,CAAC;QACxB,CAAC,MAAM;UACL/G,SAAS,CAAC,IAAI,CAAC;UACf,OAAO,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC;IACRE,aAAa,CAAC2G,aAAa,CAAC;EAC9B,CAAC,EAAE,CAACxH,QAAQ,CAAC,CAAC;EAEdnB,SAAS,CAAC,MAAM;IACd,IAAI6B,MAAM,IAAIE,UAAU,EAAE;MACxB+G,aAAa,CAAC/G,UAAU,CAAC;MACzBC,aAAa,CAAC,IAAI,CAAC;MACnBkD,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAACrD,MAAM,EAAEE,UAAU,EAAEmD,eAAe,CAAC,CAAC;EAEzClF,SAAS,CAAC,MAAM;IACd,IAAIqC,EAAE,EAAE;MACN;MACAK,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1C,SAAS,CAAC,MAAM;IACd,IAAIqB,SAAS,CAACoC,MAAM,GAAG,CAAC,EAAE;MACxBL,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrDjB,cAAc,CAACwC,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;IAClC;EACF,CAAC,EAAE,CAACvD,SAAS,CAACoC,MAAM,CAAC,CAAC;EAEtBzD,SAAS,CAAC,MAAM;IACd+I,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAC9C,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,SAAS,CAACE,MAAM,CAAC,iBAAiB,CAAC;IACnD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAENnJ,SAAS,CAAC,MAAM;IACd,IAAImB,QAAQ,IAAIE,SAAS,CAACoC,MAAM,GAAG,CAAC,EAAE;MACpCgF,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACtH,QAAQ,EAAEE,SAAS,CAAC,CAAC;EAEzBrB,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX,IAAI+B,UAAU,EAAE;QACd+G,aAAa,CAAC/G,UAAU,CAAC;QACzBC,aAAa,CAAC,IAAI,CAAC;MACrB;IACF,CAAC;EACH,CAAC,EAAE,CAACD,UAAU,CAAC,CAAC;;EAEhB;EACA;EACA,MAAMqH,YAAY,GAAGjI,QAAQ,IAAI;IAC/B2C,GAAG,EAAEzB,EAAE;IACPkB,IAAI,EAAE,iBAAiB;IACvBoB,QAAQ,EAAE,EAAE;IACZtD,SAAS,EAAE;EACb,CAAC;;EAED;EACA,MAAMgI,aAAa,GAAGhI,SAAS,CAACoC,MAAM,GAAG,CAAC,GAAGpC,SAAS,GAAG,CACvD;IACEyC,GAAG,EAAE,WAAW;IAChBP,IAAI,EAAE,qBAAqB;IAC3BQ,QAAQ,EAAE,qCAAqC;IAC/CC,IAAI,EAAE,KAAK;IACXG,OAAO,EAAE;MAAEmF,CAAC,EAAE,YAAY;MAAEC,CAAC,EAAE,gBAAgB;MAAEC,CAAC,EAAE,iBAAiB;MAAEC,CAAC,EAAE;IAAa;EACzF,CAAC,CACF;;EAID;EACA,IAAIJ,aAAa,CAAC5F,MAAM,GAAG,CAAC,IAAIlC,qBAAqB,IAAI8H,aAAa,CAAC5F,MAAM,EAAE;IAC7EjC,wBAAwB,CAAC,CAAC,CAAC;EAC7B;;EAEA;EACA,MAAMkI,eAAe,GAAGL,aAAa,CAAC9H,qBAAqB,CAAC,IAAI8H,aAAa,CAAC,CAAC,CAAC;;EAEhF;EACAjG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;IAC7ClC,QAAQ,EAAEA,QAAQ,GAAG;MAAEoC,IAAI,EAAEpC,QAAQ,CAACoC,IAAI;MAAElB,EAAE,EAAElB,QAAQ,CAAC2C;IAAI,CAAC,GAAG,IAAI;IACrE6F,cAAc,EAAEtI,SAAS,CAACoC,MAAM;IAChClC,qBAAqB;IACrBmI,eAAe,EAAErI,SAAS,CAACE,qBAAqB,CAAC,GAAG;MAClDc,EAAE,EAAEhB,SAAS,CAACE,qBAAqB,CAAC,CAACuC,GAAG;MACxCP,IAAI,EAAE,EAAArC,qBAAA,GAAAG,SAAS,CAACE,qBAAqB,CAAC,CAACgC,IAAI,cAAArC,qBAAA,uBAArCA,qBAAA,CAAuC0I,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAG,KAAK;MACrE5F,IAAI,EAAE3C,SAAS,CAACE,qBAAqB,CAAC,CAACyC,IAAI;MAC3CE,UAAU,EAAE7C,SAAS,CAACE,qBAAqB,CAAC,CAAC4C,OAAO,GAAGC,MAAM,CAACC,IAAI,CAAChD,SAAS,CAACE,qBAAqB,CAAC,CAAC4C,OAAO,CAAC,CAACV,MAAM,GAAG;IACxH,CAAC,GAAG,IAAI;IACRoG,QAAQ,EAAElI;EACZ,CAAC,CAAC;;EAEF;EACA,MAAMmI,cAAc,GAAGJ,eAAe;EACtC,MAAMK,QAAQ,GAAI,QAAOX,YAAY,CAACtF,GAAI,IAAGvC,qBAAsB,IAAGY,WAAY,IAAGkH,aAAa,CAAC5F,MAAO,EAAC;EAE3G,oBACE1C,OAAA,CAACF,iBAAiB;IAAAmJ,QAAA,eAChBjJ,OAAA,CAACH,eAAe;MAEdmD,QAAQ,EAAE+F,cAAe;MACzBG,aAAa,EAAE1I,qBAAsB;MACrCiC,cAAc,EAAE6F,aAAa,CAAC5F,MAAO;MACrCyG,cAAc,EAAEzI,eAAe,CAACF,qBAAqB,CAAE;MACvDE,eAAe,EAAEA,eAAgB,CAAC;MAAA;MAClC0I,cAAc,EAAGC,MAAM,IAAK;QAC1BhH,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE9B,qBAAqB,GAAG,CAAC,EAAE,GAAG,EAAE6I,MAAM,CAAC;QACpF,MAAMC,kBAAkB,GAAG;UACzB,GAAG5I,eAAe;UAClB,CAACF,qBAAqB,GAAG6I;QAC3B,CAAC;QACD1I,kBAAkB,CAAC2I,kBAAkB,CAAC;QACtCjH,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEgH,kBAAkB,CAAC;MAC3D,CAAE;MACFR,QAAQ,EAAElI,WAAY;MACtB2I,SAAS,EAAElB,YAAY,CAAC7F,IAAI,IAAI,MAAO;MACvCgH,aAAa,EAAE5I,WAAW,IAAI,EAAG;MACjC6I,MAAM,EAAEA,CAAA,KAAM;QACZpH,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE9B,qBAAqB,EAAE,QAAQ,EAAE8H,aAAa,CAAC5F,MAAM,CAAC;QAC5G,IAAIlC,qBAAqB,KAAK8H,aAAa,CAAC5F,MAAM,GAAG,CAAC,EAAE;UACtDL,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;UAC9D6B,eAAe,CAAC,CAAC;QACnB,CAAC,MAAM;UACL9B,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE9B,qBAAqB,GAAG,CAAC,CAAC;UACrEC,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;QACrD;MACF,CAAE;MACFkJ,UAAU,EAAEA,CAAA,KAAM;QAChBrH,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE9B,qBAAqB,CAAC;QAChF,IAAIA,qBAAqB,GAAG,CAAC,EAAE;UAC7BC,wBAAwB,CAACD,qBAAqB,GAAG,CAAC,CAAC;QACrD;MACF,CAAE;MACFmJ,QAAQ,EAAEA,CAAA,KAAM;QACdtH,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAC9D6B,eAAe,CAAC,CAAC;MACnB;IAAE,GArCG6E,QAAQ;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsCd;EAAC,GAxCoBf,QAAQ;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAyCb,CAAC;AAExB,CAAC;AAAC7J,EAAA,CAxYID,QAAQ;EAAA,QAWGd,SAAS,EACPC,WAAW,EACXC,WAAW,EACXC,WAAW;AAAA;AAAA0K,EAAA,GAdxB/J,QAAQ;AA0Yd,eAAeA,QAAQ;AAAC,IAAA+J,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}