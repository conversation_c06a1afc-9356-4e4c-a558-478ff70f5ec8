{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\NewQuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck, TbX, TbPhoto, TbEdit, TbCircleCheck, TbCircle } from 'react-icons/tb';\nimport './NewQuizRenderer.css';\n\n// Utility function to extract and normalize question data\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst extractQuestionData = question => {\n  if (!question) return null;\n  const questionText = question.name || question.question || question.text || question.title || '';\n  const questionType = question.type || question.answerType || question.questionType || 'mcq';\n  const questionOptions = question.options || question.choices || question.answers || {};\n  const correctAnswer = question.correctAnswer || question.correctOption || '';\n  const imageUrl = question.imageUrl || question.image || '';\n\n  // Normalize question type\n  let normalizedType = 'mcq';\n  const typeString = String(questionType).toLowerCase();\n  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {\n    normalizedType = 'mcq';\n  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {\n    normalizedType = 'fill';\n  } else if (typeString.includes('image') || typeString.includes('picture') || typeString.includes('diagram')) {\n    normalizedType = 'image';\n  }\n\n  // If no options are provided but it's marked as MCQ, treat as fill-in\n  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {\n    normalizedType = 'fill';\n  }\n  return {\n    text: questionText,\n    type: normalizedType,\n    options: questionOptions,\n    correctAnswer,\n    imageUrl,\n    originalQuestion: question\n  };\n};\n\n// Format time utility\nconst formatTime = seconds => {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n};\nconst NewQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false,\n  onSubmit\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // BYPASS extractQuestionData - it's causing issues, use direct question data\n  const questionData = null; // Force bypass to use fallback system\n\n  // Debug logging\n  useEffect(() => {\n    console.log('🎯 NewQuizRenderer Debug:', {\n      questionIndex,\n      totalQuestions,\n      hasQuestion: !!question,\n      hasQuestionData: !!questionData,\n      questionType: (question === null || question === void 0 ? void 0 : question.type) || (question === null || question === void 0 ? void 0 : question.answerType),\n      questionText: (question === null || question === void 0 ? void 0 : question.name) || (question === null || question === void 0 ? void 0 : question.question),\n      timeLeft\n    });\n  }, [question, questionData, questionIndex, totalQuestions, timeLeft]);\n\n  // Update current answer when selectedAnswer changes\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Handle answer selection\n  const handleAnswerChange = useCallback(answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(!!answer);\n    onAnswerChange(answer);\n  }, [onAnswerChange]);\n\n  // Handle navigation\n  const handleNext = useCallback(() => {\n    if (questionIndex === totalQuestions - 1) {\n      // Last question - submit quiz\n      if (onSubmit) {\n        onSubmit();\n      }\n    } else {\n      onNext();\n    }\n  }, [questionIndex, totalQuestions, onNext, onSubmit]);\n  const handlePrevious = useCallback(() => {\n    if (questionIndex > 0) {\n      onPrevious();\n    }\n  }, [questionIndex, onPrevious]);\n\n  // Always render - never show loading screen\n  // Create safe question data with fallbacks - be more aggressive about showing content\n  const safeQuestionData = {\n    text: (question === null || question === void 0 ? void 0 : question.name) || (question === null || question === void 0 ? void 0 : question.question) || (question === null || question === void 0 ? void 0 : question.text) || \"TEST QUESTION: What is 2 + 2?\",\n    type: (question === null || question === void 0 ? void 0 : question.type) || (question === null || question === void 0 ? void 0 : question.answerType) || 'mcq',\n    options: (question === null || question === void 0 ? void 0 : question.options) || (question === null || question === void 0 ? void 0 : question.choices) || {\n      A: (question === null || question === void 0 ? void 0 : question.optionA) || 'Option A: 3',\n      B: (question === null || question === void 0 ? void 0 : question.optionB) || 'Option B: 4',\n      C: (question === null || question === void 0 ? void 0 : question.optionC) || 'Option C: 5',\n      D: (question === null || question === void 0 ? void 0 : question.optionD) || 'Option D: 6'\n    },\n    imageUrl: (question === null || question === void 0 ? void 0 : question.imageUrl) || (question === null || question === void 0 ? void 0 : question.image) || (question === null || question === void 0 ? void 0 : question.diagram)\n  };\n\n  // Debug what we're actually rendering\n  console.log('🎯 NewQuizRenderer rendering:', {\n    hasQuestion: !!question,\n    hasQuestionData: !!questionData,\n    safeQuestionData,\n    questionIndex,\n    totalQuestions,\n    rawQuestion: question\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '100%',\n      height: '100vh',\n      background: 'lightblue',\n      padding: '20px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '10px',\n        right: '10px',\n        background: 'red',\n        color: 'white',\n        padding: '10px',\n        zIndex: 9999\n      },\n      children: [\"QUIZ RENDERER ACTIVE - Q\", questionIndex + 1, \"/\", totalQuestions]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        padding: '20px',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        style: {\n          fontSize: '24px',\n          fontWeight: 'bold'\n        },\n        children: examTitle\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Time: \", formatTime(timeLeft)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        padding: '30px',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        style: {\n          fontSize: '20px',\n          marginBottom: '20px'\n        },\n        children: safeQuestionData.text\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 9\n      }, this), safeQuestionData.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '20px',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: safeQuestionData.imageUrl,\n          alt: \"Question diagram\",\n          style: {\n            maxWidth: '100%',\n            maxHeight: '300px',\n            border: '2px solid #ccc',\n            borderRadius: '8px'\n          },\n          onError: e => {\n            e.target.style.display = 'none';\n            e.target.nextSibling.style.display = 'block';\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'none',\n            padding: '20px',\n            background: '#f0f0f0',\n            color: '#666'\n          },\n          children: \"Image not available\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 11\n      }, this), safeQuestionData.type === 'mcq' && safeQuestionData.options && Object.entries(safeQuestionData.options).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n        onClick: () => handleAnswerChange(key),\n        style: {\n          background: currentAnswer === key ? 'lightgreen' : 'lightgray',\n          padding: '15px',\n          margin: '10px 0',\n          cursor: 'pointer',\n          border: '2px solid black',\n          borderRadius: '5px'\n        },\n        children: [key, \": \", value]\n      }, key, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this)), safeQuestionData.type === 'fill' && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginTop: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          style: {\n            display: 'block',\n            marginBottom: '10px',\n            fontWeight: 'bold'\n          },\n          children: \"Your Answer:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: currentAnswer,\n          onChange: e => handleAnswerChange(e.target.value),\n          placeholder: \"Type your answer here...\",\n          style: {\n            width: '100%',\n            minHeight: '100px',\n            padding: '15px',\n            border: '2px solid #ccc',\n            borderRadius: '5px',\n            fontSize: '16px',\n            resize: 'vertical'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        padding: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onPrevious,\n        disabled: questionIndex === 0,\n        style: {\n          padding: '10px 20px',\n          marginRight: '10px',\n          background: 'gray',\n          color: 'white'\n        },\n        children: \"Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onNext,\n        style: {\n          padding: '10px 20px',\n          background: 'blue',\n          color: 'white'\n        },\n        children: questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this);\n};\n_s(NewQuizRenderer, \"/AKwHDMDUIl41hZRNod4g0BrOFc=\");\n_c = NewQuizRenderer;\nexport default NewQuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"NewQuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "AnimatePresence", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbX", "TbPhoto", "TbEdit", "TbCircleCheck", "TbCircle", "jsxDEV", "_jsxDEV", "extractQuestionData", "question", "questionText", "name", "text", "title", "questionType", "type", "answerType", "questionOptions", "options", "choices", "answers", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "imageUrl", "image", "normalizedType", "typeString", "String", "toLowerCase", "includes", "Object", "keys", "length", "originalQuestion", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "selectedOptions", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "onSubmit", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "questionData", "console", "log", "hasQuestion", "hasQuestionData", "handleAnswerChange", "answer", "handleNext", "handlePrevious", "safeQuestionData", "A", "optionA", "B", "optionB", "C", "optionC", "D", "optionD", "diagram", "rawQuestion", "style", "width", "height", "background", "padding", "children", "position", "top", "right", "color", "zIndex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "fontSize", "fontWeight", "textAlign", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "border", "borderRadius", "onError", "e", "target", "display", "nextS<PERSON>ling", "entries", "map", "key", "value", "onClick", "margin", "cursor", "marginTop", "onChange", "placeholder", "minHeight", "resize", "disabled", "marginRight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/NewQuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Tb<PERSON><PERSON>,\n  TbArrowLeft,\n  TbArrowRight,\n  TbCheck,\n  TbX,\n  TbPhoto,\n  TbEdit,\n  TbCircleCheck,\n  TbCircle\n} from 'react-icons/tb';\nimport './NewQuizRenderer.css';\n\n// Utility function to extract and normalize question data\nconst extractQuestionData = (question) => {\n  if (!question) return null;\n\n  const questionText = question.name || question.question || question.text || question.title || '';\n  const questionType = question.type || question.answerType || question.questionType || 'mcq';\n  const questionOptions = question.options || question.choices || question.answers || {};\n  const correctAnswer = question.correctAnswer || question.correctOption || '';\n  const imageUrl = question.imageUrl || question.image || '';\n\n  // Normalize question type\n  let normalizedType = 'mcq';\n  const typeString = String(questionType).toLowerCase();\n\n  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {\n    normalizedType = 'mcq';\n  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {\n    normalizedType = 'fill';\n  } else if (typeString.includes('image') || typeString.includes('picture') || typeString.includes('diagram')) {\n    normalizedType = 'image';\n  }\n\n  // If no options are provided but it's marked as MCQ, treat as fill-in\n  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {\n    normalizedType = 'fill';\n  }\n\n  return {\n    text: questionText,\n    type: normalizedType,\n    options: questionOptions,\n    correctAnswer,\n    imageUrl,\n    originalQuestion: question\n  };\n};\n\n// Format time utility\nconst formatTime = (seconds) => {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n};\n\nconst NewQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false,\n  onSubmit\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // BYPASS extractQuestionData - it's causing issues, use direct question data\n  const questionData = null; // Force bypass to use fallback system\n\n  // Debug logging\n  useEffect(() => {\n    console.log('🎯 NewQuizRenderer Debug:', {\n      questionIndex,\n      totalQuestions,\n      hasQuestion: !!question,\n      hasQuestionData: !!questionData,\n      questionType: question?.type || question?.answerType,\n      questionText: question?.name || question?.question,\n      timeLeft\n    });\n  }, [question, questionData, questionIndex, totalQuestions, timeLeft]);\n\n  // Update current answer when selectedAnswer changes\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Handle answer selection\n  const handleAnswerChange = useCallback((answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(!!answer);\n    onAnswerChange(answer);\n  }, [onAnswerChange]);\n\n  // Handle navigation\n  const handleNext = useCallback(() => {\n    if (questionIndex === totalQuestions - 1) {\n      // Last question - submit quiz\n      if (onSubmit) {\n        onSubmit();\n      }\n    } else {\n      onNext();\n    }\n  }, [questionIndex, totalQuestions, onNext, onSubmit]);\n\n  const handlePrevious = useCallback(() => {\n    if (questionIndex > 0) {\n      onPrevious();\n    }\n  }, [questionIndex, onPrevious]);\n\n  // Always render - never show loading screen\n  // Create safe question data with fallbacks - be more aggressive about showing content\n  const safeQuestionData = {\n    text: question?.name || question?.question || question?.text || \"TEST QUESTION: What is 2 + 2?\",\n    type: question?.type || question?.answerType || 'mcq',\n    options: question?.options || question?.choices || {\n      A: question?.optionA || 'Option A: 3',\n      B: question?.optionB || 'Option B: 4',\n      C: question?.optionC || 'Option C: 5',\n      D: question?.optionD || 'Option D: 6'\n    },\n    imageUrl: question?.imageUrl || question?.image || question?.diagram\n  };\n\n  // Debug what we're actually rendering\n  console.log('🎯 NewQuizRenderer rendering:', {\n    hasQuestion: !!question,\n    hasQuestionData: !!questionData,\n    safeQuestionData,\n    questionIndex,\n    totalQuestions,\n    rawQuestion: question\n  });\n\n  return (\n    <div style={{width: '100%', height: '100vh', background: 'lightblue', padding: '20px'}}>\n      {/* DEBUG: Test if component renders */}\n      <div style={{position: 'fixed', top: '10px', right: '10px', background: 'red', color: 'white', padding: '10px', zIndex: 9999}}>\n        QUIZ RENDERER ACTIVE - Q{questionIndex + 1}/{totalQuestions}\n      </div>\n\n      {/* SIMPLE HEADER */}\n      <div style={{background: 'white', padding: '20px', marginBottom: '20px'}}>\n        <h1 style={{fontSize: '24px', fontWeight: 'bold'}}>{examTitle}</h1>\n        <p>Question {questionIndex + 1} of {totalQuestions}</p>\n        <p>Time: {formatTime(timeLeft)}</p>\n      </div>\n\n      {/* SIMPLE QUESTION */}\n      <div style={{background: 'white', padding: '30px', marginBottom: '20px'}}>\n        <h2 style={{fontSize: '20px', marginBottom: '20px'}}>{safeQuestionData.text}</h2>\n\n        {/* Image Display */}\n        {safeQuestionData.imageUrl && (\n          <div style={{marginBottom: '20px', textAlign: 'center'}}>\n            <img\n              src={safeQuestionData.imageUrl}\n              alt=\"Question diagram\"\n              style={{\n                maxWidth: '100%',\n                maxHeight: '300px',\n                border: '2px solid #ccc',\n                borderRadius: '8px'\n              }}\n              onError={(e) => {\n                e.target.style.display = 'none';\n                e.target.nextSibling.style.display = 'block';\n              }}\n            />\n            <div style={{display: 'none', padding: '20px', background: '#f0f0f0', color: '#666'}}>\n              Image not available\n            </div>\n          </div>\n        )}\n\n        {/* Multiple Choice Questions */}\n        {safeQuestionData.type === 'mcq' && safeQuestionData.options && Object.entries(safeQuestionData.options).map(([key, value]) => (\n          <div\n            key={key}\n            onClick={() => handleAnswerChange(key)}\n            style={{\n              background: currentAnswer === key ? 'lightgreen' : 'lightgray',\n              padding: '15px',\n              margin: '10px 0',\n              cursor: 'pointer',\n              border: '2px solid black',\n              borderRadius: '5px'\n            }}\n          >\n            {key}: {value}\n          </div>\n        ))}\n\n        {/* Fill-in-the-Blank Questions */}\n        {safeQuestionData.type === 'fill' && (\n          <div style={{marginTop: '20px'}}>\n            <label style={{display: 'block', marginBottom: '10px', fontWeight: 'bold'}}>\n              Your Answer:\n            </label>\n            <textarea\n              value={currentAnswer}\n              onChange={(e) => handleAnswerChange(e.target.value)}\n              placeholder=\"Type your answer here...\"\n              style={{\n                width: '100%',\n                minHeight: '100px',\n                padding: '15px',\n                border: '2px solid #ccc',\n                borderRadius: '5px',\n                fontSize: '16px',\n                resize: 'vertical'\n              }}\n            />\n          </div>\n        )}\n      </div>\n\n      {/* SIMPLE NAVIGATION */}\n      <div style={{background: 'white', padding: '20px'}}>\n        <button\n          onClick={onPrevious}\n          disabled={questionIndex === 0}\n          style={{padding: '10px 20px', marginRight: '10px', background: 'gray', color: 'white'}}\n        >\n          Previous\n        </button>\n        <button\n          onClick={onNext}\n          style={{padding: '10px 20px', background: 'blue', color: 'white'}}\n        >\n          {questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default NewQuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,aAAa,EACbC,QAAQ,QACH,gBAAgB;AACvB,OAAO,uBAAuB;;AAE9B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAIC,QAAQ,IAAK;EACxC,IAAI,CAACA,QAAQ,EAAE,OAAO,IAAI;EAE1B,MAAMC,YAAY,GAAGD,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACA,QAAQ,IAAIA,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACI,KAAK,IAAI,EAAE;EAChG,MAAMC,YAAY,GAAGL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACO,UAAU,IAAIP,QAAQ,CAACK,YAAY,IAAI,KAAK;EAC3F,MAAMG,eAAe,GAAGR,QAAQ,CAACS,OAAO,IAAIT,QAAQ,CAACU,OAAO,IAAIV,QAAQ,CAACW,OAAO,IAAI,CAAC,CAAC;EACtF,MAAMC,aAAa,GAAGZ,QAAQ,CAACY,aAAa,IAAIZ,QAAQ,CAACa,aAAa,IAAI,EAAE;EAC5E,MAAMC,QAAQ,GAAGd,QAAQ,CAACc,QAAQ,IAAId,QAAQ,CAACe,KAAK,IAAI,EAAE;;EAE1D;EACA,IAAIC,cAAc,GAAG,KAAK;EAC1B,MAAMC,UAAU,GAAGC,MAAM,CAACb,YAAY,CAAC,CAACc,WAAW,CAAC,CAAC;EAErD,IAAIF,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,IAAIH,UAAU,KAAK,KAAK,IAAIA,UAAU,CAACG,QAAQ,CAAC,UAAU,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,EAAE;IAC7HJ,cAAc,GAAG,KAAK;EACxB,CAAC,MAAM,IAAIC,UAAU,CAACG,QAAQ,CAAC,MAAM,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,UAAU,KAAK,MAAM,IAAIA,UAAU,CAACG,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC9HJ,cAAc,GAAG,MAAM;EACzB,CAAC,MAAM,IAAIC,UAAU,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,SAAS,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,SAAS,CAAC,EAAE;IAC3GJ,cAAc,GAAG,OAAO;EAC1B;;EAEA;EACA,IAAIA,cAAc,KAAK,KAAK,KAAK,CAACR,eAAe,IAAIa,MAAM,CAACC,IAAI,CAACd,eAAe,CAAC,CAACe,MAAM,KAAK,CAAC,CAAC,EAAE;IAC/FP,cAAc,GAAG,MAAM;EACzB;EAEA,OAAO;IACLb,IAAI,EAAEF,YAAY;IAClBK,IAAI,EAAEU,cAAc;IACpBP,OAAO,EAAED,eAAe;IACxBI,aAAa;IACbE,QAAQ;IACRU,gBAAgB,EAAExB;EACpB,CAAC;AACH,CAAC;;AAED;AACA,MAAMyB,UAAU,GAAIC,OAAO,IAAK;EAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;EACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;EACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;AACrE,CAAC;AAED,MAAMC,eAAe,GAAGA,CAAC;EACvBjC,QAAQ;EACRkC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,eAAe,GAAG,CAAC,CAAC;EACpBC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG,KAAK;EACrBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhE,QAAQ,CAACqD,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAMmE,YAAY,GAAG,IAAI,CAAC,CAAC;;EAE3B;EACAlE,SAAS,CAAC,MAAM;IACdmE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvClB,aAAa;MACbC,cAAc;MACdkB,WAAW,EAAE,CAAC,CAACrD,QAAQ;MACvBsD,eAAe,EAAE,CAAC,CAACJ,YAAY;MAC/B7C,YAAY,EAAE,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,IAAI,MAAIN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,UAAU;MACpDN,YAAY,EAAE,CAAAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,IAAI,MAAIF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEA,QAAQ;MAClDuC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvC,QAAQ,EAAEkD,YAAY,EAAEhB,aAAa,EAAEC,cAAc,EAAEI,QAAQ,CAAC,CAAC;;EAErE;EACAvD,SAAS,CAAC,MAAM;IACd+D,gBAAgB,CAACX,cAAc,IAAI,EAAE,CAAC;IACtCa,aAAa,CAAC,CAAC,CAACb,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;;EAEnC;EACA,MAAMqB,kBAAkB,GAAGtE,WAAW,CAAEuE,MAAM,IAAK;IACjDT,gBAAgB,CAACS,MAAM,CAAC;IACxBP,aAAa,CAAC,CAAC,CAACO,MAAM,CAAC;IACvBlB,cAAc,CAACkB,MAAM,CAAC;EACxB,CAAC,EAAE,CAAClB,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMmB,UAAU,GAAGxE,WAAW,CAAC,MAAM;IACnC,IAAIiD,aAAa,KAAKC,cAAc,GAAG,CAAC,EAAE;MACxC;MACA,IAAIS,QAAQ,EAAE;QACZA,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,MAAM;MACLJ,MAAM,CAAC,CAAC;IACV;EACF,CAAC,EAAE,CAACN,aAAa,EAAEC,cAAc,EAAEK,MAAM,EAAEI,QAAQ,CAAC,CAAC;EAErD,MAAMc,cAAc,GAAGzE,WAAW,CAAC,MAAM;IACvC,IAAIiD,aAAa,GAAG,CAAC,EAAE;MACrBO,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACP,aAAa,EAAEO,UAAU,CAAC,CAAC;;EAE/B;EACA;EACA,MAAMkB,gBAAgB,GAAG;IACvBxD,IAAI,EAAE,CAAAH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,IAAI,MAAIF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEA,QAAQ,MAAIA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,IAAI,KAAI,+BAA+B;IAC/FG,IAAI,EAAE,CAAAN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,IAAI,MAAIN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,UAAU,KAAI,KAAK;IACrDE,OAAO,EAAE,CAAAT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,OAAO,MAAIT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEU,OAAO,KAAI;MACjDkD,CAAC,EAAE,CAAA5D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6D,OAAO,KAAI,aAAa;MACrCC,CAAC,EAAE,CAAA9D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE+D,OAAO,KAAI,aAAa;MACrCC,CAAC,EAAE,CAAAhE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiE,OAAO,KAAI,aAAa;MACrCC,CAAC,EAAE,CAAAlE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmE,OAAO,KAAI;IAC1B,CAAC;IACDrD,QAAQ,EAAE,CAAAd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEc,QAAQ,MAAId,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEe,KAAK,MAAIf,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEoE,OAAO;EACtE,CAAC;;EAED;EACAjB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;IAC3CC,WAAW,EAAE,CAAC,CAACrD,QAAQ;IACvBsD,eAAe,EAAE,CAAC,CAACJ,YAAY;IAC/BS,gBAAgB;IAChBzB,aAAa;IACbC,cAAc;IACdkC,WAAW,EAAErE;EACf,CAAC,CAAC;EAEF,oBACEF,OAAA;IAAKwE,KAAK,EAAE;MAACC,KAAK,EAAE,MAAM;MAAEC,MAAM,EAAE,OAAO;MAAEC,UAAU,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAM,CAAE;IAAAC,QAAA,gBAErF7E,OAAA;MAAKwE,KAAK,EAAE;QAACM,QAAQ,EAAE,OAAO;QAAEC,GAAG,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEL,UAAU,EAAE,KAAK;QAAEM,KAAK,EAAE,OAAO;QAAEL,OAAO,EAAE,MAAM;QAAEM,MAAM,EAAE;MAAI,CAAE;MAAAL,QAAA,GAAC,0BACrG,EAACzC,aAAa,GAAG,CAAC,EAAC,GAAC,EAACC,cAAc;IAAA;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxD,CAAC,eAGNtF,OAAA;MAAKwE,KAAK,EAAE;QAACG,UAAU,EAAE,OAAO;QAAEC,OAAO,EAAE,MAAM;QAAEW,YAAY,EAAE;MAAM,CAAE;MAAAV,QAAA,gBACvE7E,OAAA;QAAIwE,KAAK,EAAE;UAACgB,QAAQ,EAAE,MAAM;UAAEC,UAAU,EAAE;QAAM,CAAE;QAAAZ,QAAA,EAAEjC;MAAS;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnEtF,OAAA;QAAA6E,QAAA,GAAG,WAAS,EAACzC,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;MAAA;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvDtF,OAAA;QAAA6E,QAAA,GAAG,QAAM,EAAClD,UAAU,CAACc,QAAQ,CAAC;MAAA;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,eAGNtF,OAAA;MAAKwE,KAAK,EAAE;QAACG,UAAU,EAAE,OAAO;QAAEC,OAAO,EAAE,MAAM;QAAEW,YAAY,EAAE;MAAM,CAAE;MAAAV,QAAA,gBACvE7E,OAAA;QAAIwE,KAAK,EAAE;UAACgB,QAAQ,EAAE,MAAM;UAAED,YAAY,EAAE;QAAM,CAAE;QAAAV,QAAA,EAAEhB,gBAAgB,CAACxD;MAAI;QAAA8E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EAGhFzB,gBAAgB,CAAC7C,QAAQ,iBACxBhB,OAAA;QAAKwE,KAAK,EAAE;UAACe,YAAY,EAAE,MAAM;UAAEG,SAAS,EAAE;QAAQ,CAAE;QAAAb,QAAA,gBACtD7E,OAAA;UACE2F,GAAG,EAAE9B,gBAAgB,CAAC7C,QAAS;UAC/B4E,GAAG,EAAC,kBAAkB;UACtBpB,KAAK,EAAE;YACLqB,QAAQ,EAAE,MAAM;YAChBC,SAAS,EAAE,OAAO;YAClBC,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE;UAChB,CAAE;UACFC,OAAO,EAAGC,CAAC,IAAK;YACdA,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAC4B,OAAO,GAAG,MAAM;YAC/BF,CAAC,CAACC,MAAM,CAACE,WAAW,CAAC7B,KAAK,CAAC4B,OAAO,GAAG,OAAO;UAC9C;QAAE;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFtF,OAAA;UAAKwE,KAAK,EAAE;YAAC4B,OAAO,EAAE,MAAM;YAAExB,OAAO,EAAE,MAAM;YAAED,UAAU,EAAE,SAAS;YAAEM,KAAK,EAAE;UAAM,CAAE;UAAAJ,QAAA,EAAC;QAEtF;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGAzB,gBAAgB,CAACrD,IAAI,KAAK,KAAK,IAAIqD,gBAAgB,CAAClD,OAAO,IAAIY,MAAM,CAAC+E,OAAO,CAACzC,gBAAgB,CAAClD,OAAO,CAAC,CAAC4F,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,kBACxHzG,OAAA;QAEE0G,OAAO,EAAEA,CAAA,KAAMjD,kBAAkB,CAAC+C,GAAG,CAAE;QACvChC,KAAK,EAAE;UACLG,UAAU,EAAE3B,aAAa,KAAKwD,GAAG,GAAG,YAAY,GAAG,WAAW;UAC9D5B,OAAO,EAAE,MAAM;UACf+B,MAAM,EAAE,QAAQ;UAChBC,MAAM,EAAE,SAAS;UACjBb,MAAM,EAAE,iBAAiB;UACzBC,YAAY,EAAE;QAChB,CAAE;QAAAnB,QAAA,GAED2B,GAAG,EAAC,IAAE,EAACC,KAAK;MAAA,GAXRD,GAAG;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYL,CACN,CAAC,EAGDzB,gBAAgB,CAACrD,IAAI,KAAK,MAAM,iBAC/BR,OAAA;QAAKwE,KAAK,EAAE;UAACqC,SAAS,EAAE;QAAM,CAAE;QAAAhC,QAAA,gBAC9B7E,OAAA;UAAOwE,KAAK,EAAE;YAAC4B,OAAO,EAAE,OAAO;YAAEb,YAAY,EAAE,MAAM;YAAEE,UAAU,EAAE;UAAM,CAAE;UAAAZ,QAAA,EAAC;QAE5E;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRtF,OAAA;UACEyG,KAAK,EAAEzD,aAAc;UACrB8D,QAAQ,EAAGZ,CAAC,IAAKzC,kBAAkB,CAACyC,CAAC,CAACC,MAAM,CAACM,KAAK,CAAE;UACpDM,WAAW,EAAC,0BAA0B;UACtCvC,KAAK,EAAE;YACLC,KAAK,EAAE,MAAM;YACbuC,SAAS,EAAE,OAAO;YAClBpC,OAAO,EAAE,MAAM;YACfmB,MAAM,EAAE,gBAAgB;YACxBC,YAAY,EAAE,KAAK;YACnBR,QAAQ,EAAE,MAAM;YAChByB,MAAM,EAAE;UACV;QAAE;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNtF,OAAA;MAAKwE,KAAK,EAAE;QAACG,UAAU,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAM,CAAE;MAAAC,QAAA,gBACjD7E,OAAA;QACE0G,OAAO,EAAE/D,UAAW;QACpBuE,QAAQ,EAAE9E,aAAa,KAAK,CAAE;QAC9BoC,KAAK,EAAE;UAACI,OAAO,EAAE,WAAW;UAAEuC,WAAW,EAAE,MAAM;UAAExC,UAAU,EAAE,MAAM;UAAEM,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,EACxF;MAED;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtF,OAAA;QACE0G,OAAO,EAAEhE,MAAO;QAChB8B,KAAK,EAAE;UAACI,OAAO,EAAE,WAAW;UAAED,UAAU,EAAE,MAAM;UAAEM,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,EAEjEzC,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,QAAQ,GAAG;MAAM;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CA7LIZ,eAAe;AAAAiF,EAAA,GAAfjF,eAAe;AA+LrB,eAAeA,eAAe;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}