{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\20\\\\New folder\\\\client\\\\src\\\\components\\\\NewQuizRenderer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { TbClock, TbArrowLeft, TbArrowRight, TbCheck, TbX, TbPhoto, TbEdit, TbCircleCheck, TbCircle } from 'react-icons/tb';\nimport './NewQuizRenderer.css';\n\n// Utility function to extract and normalize question data\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst extractQuestionData = question => {\n  if (!question) return null;\n  const questionText = question.name || question.question || question.text || question.title || '';\n  const questionType = question.type || question.answerType || question.questionType || 'mcq';\n  const questionOptions = question.options || question.choices || question.answers || {};\n  const correctAnswer = question.correctAnswer || question.correctOption || '';\n  const imageUrl = question.imageUrl || question.image || '';\n\n  // Normalize question type\n  let normalizedType = 'mcq';\n  const typeString = String(questionType).toLowerCase();\n  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {\n    normalizedType = 'mcq';\n  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {\n    normalizedType = 'fill';\n  } else if (typeString.includes('image') || typeString.includes('picture') || typeString.includes('diagram')) {\n    normalizedType = 'image';\n  }\n\n  // If no options are provided but it's marked as MCQ, treat as fill-in\n  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {\n    normalizedType = 'fill';\n  }\n  return {\n    text: questionText,\n    type: normalizedType,\n    options: questionOptions,\n    correctAnswer,\n    imageUrl,\n    originalQuestion: question\n  };\n};\n\n// Format time utility\nconst formatTime = seconds => {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n};\nconst NewQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false,\n  onSubmit\n}) => {\n  _s();\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // BYPASS extractQuestionData - it's causing issues, use direct question data\n  const questionData = null; // Force bypass to use fallback system\n\n  // Debug logging\n  useEffect(() => {\n    console.log('🎯 NewQuizRenderer Debug:', {\n      questionIndex,\n      totalQuestions,\n      hasQuestion: !!question,\n      hasQuestionData: !!questionData,\n      questionType: (question === null || question === void 0 ? void 0 : question.type) || (question === null || question === void 0 ? void 0 : question.answerType),\n      questionText: (question === null || question === void 0 ? void 0 : question.name) || (question === null || question === void 0 ? void 0 : question.question),\n      timeLeft\n    });\n  }, [question, questionData, questionIndex, totalQuestions, timeLeft]);\n\n  // Update current answer when selectedAnswer changes\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Handle answer selection\n  const handleAnswerChange = useCallback(answer => {\n    setCurrentAnswer(answer);\n    setIsAnswered(!!answer);\n    onAnswerChange(answer);\n  }, [onAnswerChange]);\n\n  // Handle navigation\n  const handleNext = useCallback(() => {\n    if (questionIndex === totalQuestions - 1) {\n      // Last question - submit quiz\n      if (onSubmit) {\n        onSubmit();\n      }\n    } else {\n      onNext();\n    }\n  }, [questionIndex, totalQuestions, onNext, onSubmit]);\n  const handlePrevious = useCallback(() => {\n    if (questionIndex > 0) {\n      onPrevious();\n    }\n  }, [questionIndex, onPrevious]);\n\n  // Always render - never show loading screen\n  // Create safe question data with fallbacks - be more aggressive about showing content\n  const safeQuestionData = {\n    text: (question === null || question === void 0 ? void 0 : question.name) || (question === null || question === void 0 ? void 0 : question.question) || (question === null || question === void 0 ? void 0 : question.text) || \"TEST QUESTION: What is 2 + 2?\",\n    type: (question === null || question === void 0 ? void 0 : question.type) || (question === null || question === void 0 ? void 0 : question.answerType) || 'mcq',\n    options: (question === null || question === void 0 ? void 0 : question.options) || (question === null || question === void 0 ? void 0 : question.choices) || {\n      A: (question === null || question === void 0 ? void 0 : question.optionA) || 'Option A: 3',\n      B: (question === null || question === void 0 ? void 0 : question.optionB) || 'Option B: 4',\n      C: (question === null || question === void 0 ? void 0 : question.optionC) || 'Option C: 5',\n      D: (question === null || question === void 0 ? void 0 : question.optionD) || 'Option D: 6'\n    },\n    imageUrl: (question === null || question === void 0 ? void 0 : question.imageUrl) || (question === null || question === void 0 ? void 0 : question.image) || (question === null || question === void 0 ? void 0 : question.diagram)\n  };\n\n  // Debug what we're actually rendering\n  console.log('🎯 NewQuizRenderer rendering:', {\n    hasQuestion: !!question,\n    hasQuestionData: !!questionData,\n    safeQuestionData,\n    questionIndex,\n    totalQuestions,\n    rawQuestion: question\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto px-4 py-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-gray-900 truncate\",\n              children: examTitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mt-1\",\n              children: [\"Question \", questionIndex + 1, \" of \", totalQuestions]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center gap-2 px-4 py-2 rounded-full font-bold text-base transition-all ${isTimeWarning ? 'bg-red-100 text-red-700 animate-pulse' : 'bg-blue-100 text-blue-700'}`,\n            children: [/*#__PURE__*/_jsxDEV(TbClock, {\n              className: \"w-5 h-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: formatTime(timeLeft)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-gray-200 rounded-full h-2\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-500\",\n              style: {\n                width: `${(questionIndex + 1) / totalQuestions * 100}%`\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-between text-xs text-gray-500 mt-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Progress\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: [Math.round((questionIndex + 1) / totalQuestions * 100), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto px-4 py-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `px-3 py-1 rounded-full text-xs font-semibold ${safeQuestionData.type === 'mcq' ? 'bg-blue-100 text-blue-800' : safeQuestionData.type === 'fill' ? 'bg-green-100 text-green-800' : 'bg-purple-100 text-purple-800'}`,\n              children: safeQuestionData.type === 'mcq' ? 'Multiple Choice' : safeQuestionData.type === 'fill' ? 'Fill in the Blank' : 'Image Question'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 15\n            }, this), currentAnswer && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center gap-1 text-white text-sm font-medium\",\n              children: [/*#__PURE__*/_jsxDEV(TbCheck, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this), \"Answered\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6 max-h-[60vh] overflow-y-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-xl font-semibold text-gray-900 mb-4 leading-relaxed\",\n              children: safeQuestionData.text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), safeQuestionData.imageUrl && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: safeQuestionData.imageUrl,\n                  alt: \"Question diagram\",\n                  className: \"w-full max-w-2xl mx-auto h-auto object-contain max-h-80\",\n                  style: {\n                    display: 'block'\n                  },\n                  onError: e => {\n                    e.target.style.display = 'none';\n                    e.target.nextSibling.style.display = 'flex';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hidden items-center justify-center h-48 text-gray-500\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(TbPhoto, {\n                      className: \"w-12 h-12 mx-auto mb-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 241,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      children: \"Image not available\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 240,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 239,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [safeQuestionData.type === 'mcq' && safeQuestionData.options && Object.entries(safeQuestionData.options).map(([key, value]) => {\n              const isSelected = currentAnswer === key;\n              return /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => handleAnswerChange(key),\n                className: `w-full p-4 rounded-xl border-2 text-left transition-all duration-200 ${isSelected ? 'border-blue-500 bg-blue-50 shadow-md' : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold ${isSelected ? 'border-blue-500 bg-blue-500 text-white' : 'border-gray-300 text-gray-600'}`,\n                    children: isSelected ? /*#__PURE__*/_jsxDEV(TbCheck, {\n                      className: \"w-5 h-5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 39\n                    }, this) : key\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 266,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-900 font-medium\",\n                    children: value\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 273,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 21\n                }, this)\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this);\n            }), safeQuestionData.type === 'fill' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-4\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border-2 border-green-200\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-2 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(TbEdit, {\n                      className: \"w-5 h-5 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 285,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 284,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    className: \"text-sm font-semibold text-gray-700\",\n                    children: \"Your Answer:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 283,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: currentAnswer,\n                  onChange: e => handleAnswerChange(e.target.value),\n                  placeholder: \"Type your answer here...\",\n                  className: \"w-full p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all\",\n                  rows: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        background: 'white',\n        padding: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onPrevious,\n        disabled: questionIndex === 0,\n        style: {\n          padding: '10px 20px',\n          marginRight: '10px',\n          background: 'gray',\n          color: 'white'\n        },\n        children: \"Previous\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onNext,\n        style: {\n          padding: '10px 20px',\n          background: 'blue',\n          color: 'white'\n        },\n        children: questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 5\n  }, this);\n};\n_s(NewQuizRenderer, \"/AKwHDMDUIl41hZRNod4g0BrOFc=\");\n_c = NewQuizRenderer;\nexport default NewQuizRenderer;\nvar _c;\n$RefreshReg$(_c, \"NewQuizRenderer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "motion", "AnimatePresence", "TbClock", "TbArrowLeft", "TbArrowRight", "TbCheck", "TbX", "TbPhoto", "TbEdit", "TbCircleCheck", "TbCircle", "jsxDEV", "_jsxDEV", "extractQuestionData", "question", "questionText", "name", "text", "title", "questionType", "type", "answerType", "questionOptions", "options", "choices", "answers", "<PERSON><PERSON><PERSON><PERSON>", "correctOption", "imageUrl", "image", "normalizedType", "typeString", "String", "toLowerCase", "includes", "Object", "keys", "length", "originalQuestion", "formatTime", "seconds", "minutes", "Math", "floor", "remainingSeconds", "toString", "padStart", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "questionIndex", "totalQuestions", "<PERSON><PERSON><PERSON><PERSON>", "selectedOptions", "onAnswerChange", "timeLeft", "onNext", "onPrevious", "examTitle", "isTimeWarning", "onSubmit", "_s", "currentAnswer", "setCurrentAnswer", "isAnswered", "setIsAnswered", "questionData", "console", "log", "hasQuestion", "hasQuestionData", "handleAnswerChange", "answer", "handleNext", "handlePrevious", "safeQuestionData", "A", "optionA", "B", "optionB", "C", "optionC", "D", "optionD", "diagram", "rawQuestion", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "width", "round", "src", "alt", "display", "onError", "e", "target", "nextS<PERSON>ling", "entries", "map", "key", "value", "isSelected", "onClick", "onChange", "placeholder", "rows", "background", "padding", "disabled", "marginRight", "color", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/20/New folder/client/src/components/NewQuizRenderer.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport {\n  Tb<PERSON><PERSON>,\n  TbArrowLeft,\n  TbArrowRight,\n  TbCheck,\n  TbX,\n  TbPhoto,\n  TbEdit,\n  TbCircleCheck,\n  TbCircle\n} from 'react-icons/tb';\nimport './NewQuizRenderer.css';\n\n// Utility function to extract and normalize question data\nconst extractQuestionData = (question) => {\n  if (!question) return null;\n\n  const questionText = question.name || question.question || question.text || question.title || '';\n  const questionType = question.type || question.answerType || question.questionType || 'mcq';\n  const questionOptions = question.options || question.choices || question.answers || {};\n  const correctAnswer = question.correctAnswer || question.correctOption || '';\n  const imageUrl = question.imageUrl || question.image || '';\n\n  // Normalize question type\n  let normalizedType = 'mcq';\n  const typeString = String(questionType).toLowerCase();\n\n  if (typeString.includes('option') || typeString === 'mcq' || typeString.includes('multiple') || typeString.includes('choice')) {\n    normalizedType = 'mcq';\n  } else if (typeString.includes('fill') || typeString.includes('blank') || typeString === 'text' || typeString.includes('free')) {\n    normalizedType = 'fill';\n  } else if (typeString.includes('image') || typeString.includes('picture') || typeString.includes('diagram')) {\n    normalizedType = 'image';\n  }\n\n  // If no options are provided but it's marked as MCQ, treat as fill-in\n  if (normalizedType === 'mcq' && (!questionOptions || Object.keys(questionOptions).length === 0)) {\n    normalizedType = 'fill';\n  }\n\n  return {\n    text: questionText,\n    type: normalizedType,\n    options: questionOptions,\n    correctAnswer,\n    imageUrl,\n    originalQuestion: question\n  };\n};\n\n// Format time utility\nconst formatTime = (seconds) => {\n  const minutes = Math.floor(seconds / 60);\n  const remainingSeconds = seconds % 60;\n  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n};\n\nconst NewQuizRenderer = ({\n  question,\n  questionIndex,\n  totalQuestions,\n  selectedAnswer,\n  selectedOptions = {},\n  onAnswerChange,\n  timeLeft,\n  onNext,\n  onPrevious,\n  examTitle = \"Quiz\",\n  isTimeWarning = false,\n  onSubmit\n}) => {\n  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');\n  const [isAnswered, setIsAnswered] = useState(false);\n\n  // BYPASS extractQuestionData - it's causing issues, use direct question data\n  const questionData = null; // Force bypass to use fallback system\n\n  // Debug logging\n  useEffect(() => {\n    console.log('🎯 NewQuizRenderer Debug:', {\n      questionIndex,\n      totalQuestions,\n      hasQuestion: !!question,\n      hasQuestionData: !!questionData,\n      questionType: question?.type || question?.answerType,\n      questionText: question?.name || question?.question,\n      timeLeft\n    });\n  }, [question, questionData, questionIndex, totalQuestions, timeLeft]);\n\n  // Update current answer when selectedAnswer changes\n  useEffect(() => {\n    setCurrentAnswer(selectedAnswer || '');\n    setIsAnswered(!!selectedAnswer);\n  }, [selectedAnswer, questionIndex]);\n\n  // Handle answer selection\n  const handleAnswerChange = useCallback((answer) => {\n    setCurrentAnswer(answer);\n    setIsAnswered(!!answer);\n    onAnswerChange(answer);\n  }, [onAnswerChange]);\n\n  // Handle navigation\n  const handleNext = useCallback(() => {\n    if (questionIndex === totalQuestions - 1) {\n      // Last question - submit quiz\n      if (onSubmit) {\n        onSubmit();\n      }\n    } else {\n      onNext();\n    }\n  }, [questionIndex, totalQuestions, onNext, onSubmit]);\n\n  const handlePrevious = useCallback(() => {\n    if (questionIndex > 0) {\n      onPrevious();\n    }\n  }, [questionIndex, onPrevious]);\n\n  // Always render - never show loading screen\n  // Create safe question data with fallbacks - be more aggressive about showing content\n  const safeQuestionData = {\n    text: question?.name || question?.question || question?.text || \"TEST QUESTION: What is 2 + 2?\",\n    type: question?.type || question?.answerType || 'mcq',\n    options: question?.options || question?.choices || {\n      A: question?.optionA || 'Option A: 3',\n      B: question?.optionB || 'Option B: 4',\n      C: question?.optionC || 'Option C: 5',\n      D: question?.optionD || 'Option D: 6'\n    },\n    imageUrl: question?.imageUrl || question?.image || question?.diagram\n  };\n\n  // Debug what we're actually rendering\n  console.log('🎯 NewQuizRenderer rendering:', {\n    hasQuestion: !!question,\n    hasQuestionData: !!questionData,\n    safeQuestionData,\n    questionIndex,\n    totalQuestions,\n    rawQuestion: question\n  });\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {/* Header with Timer and Quiz Info */}\n      <div className=\"bg-white shadow-lg border-b-2 border-blue-200 sticky top-0 z-50\">\n        <div className=\"max-w-6xl mx-auto px-4 py-4\">\n          <div className=\"flex items-center justify-between\">\n            {/* Quiz Title and Question Counter */}\n            <div className=\"flex-1\">\n              <h1 className=\"text-xl font-bold text-gray-900 truncate\">\n                {examTitle}\n              </h1>\n              <p className=\"text-sm text-gray-600 mt-1\">\n                Question {questionIndex + 1} of {totalQuestions}\n              </p>\n            </div>\n\n            {/* Timer */}\n            <div className={`flex items-center gap-2 px-4 py-2 rounded-full font-bold text-base transition-all ${\n              isTimeWarning\n                ? 'bg-red-100 text-red-700 animate-pulse'\n                : 'bg-blue-100 text-blue-700'\n            }`}>\n              <TbClock className=\"w-5 h-5\" />\n              <span>{formatTime(timeLeft)}</span>\n            </div>\n          </div>\n\n          {/* Progress Bar */}\n          <div className=\"mt-4\">\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <div\n                className=\"bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-500\"\n                style={{ width: `${((questionIndex + 1) / totalQuestions) * 100}%` }}\n              />\n            </div>\n            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n              <span>Progress</span>\n              <span>{Math.round(((questionIndex + 1) / totalQuestions) * 100)}%</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"max-w-4xl mx-auto px-4 py-6\">\n        <div className=\"bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden\">\n          {/* Question Header */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className={`px-3 py-1 rounded-full text-xs font-semibold ${\n                safeQuestionData.type === 'mcq'\n                  ? 'bg-blue-100 text-blue-800'\n                  : safeQuestionData.type === 'fill'\n                  ? 'bg-green-100 text-green-800'\n                  : 'bg-purple-100 text-purple-800'\n              }`}>\n                {safeQuestionData.type === 'mcq' ? 'Multiple Choice' :\n                 safeQuestionData.type === 'fill' ? 'Fill in the Blank' :\n                 'Image Question'}\n              </div>\n              {currentAnswer && (\n                <div className=\"flex items-center gap-1 text-white text-sm font-medium\">\n                  <TbCheck className=\"w-4 h-4\" />\n                  Answered\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Question Content */}\n          <div className=\"p-6 max-h-[60vh] overflow-y-auto\">\n            {/* Question Text */}\n            <div className=\"mb-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4 leading-relaxed\">\n                {safeQuestionData.text}\n              </h2>\n\n              {/* Image Display */}\n              {safeQuestionData.imageUrl && (\n                <div className=\"mb-6\">\n                  <div className=\"relative bg-gray-50 rounded-xl overflow-hidden border border-gray-200\">\n                    <img\n                      src={safeQuestionData.imageUrl}\n                      alt=\"Question diagram\"\n                      className=\"w-full max-w-2xl mx-auto h-auto object-contain max-h-80\"\n                      style={{ display: 'block' }}\n                      onError={(e) => {\n                        e.target.style.display = 'none';\n                        e.target.nextSibling.style.display = 'flex';\n                      }}\n                    />\n                    <div className=\"hidden items-center justify-center h-48 text-gray-500\">\n                      <div className=\"text-center\">\n                        <TbPhoto className=\"w-12 h-12 mx-auto mb-2\" />\n                        <p>Image not available</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n\n            {/* Answer Options */}\n            <div className=\"space-y-4\">\n              {/* Multiple Choice Questions */}\n              {safeQuestionData.type === 'mcq' && safeQuestionData.options && Object.entries(safeQuestionData.options).map(([key, value]) => {\n                const isSelected = currentAnswer === key;\n                return (\n                  <button\n                    key={key}\n                    onClick={() => handleAnswerChange(key)}\n                    className={`w-full p-4 rounded-xl border-2 text-left transition-all duration-200 ${\n                      isSelected\n                        ? 'border-blue-500 bg-blue-50 shadow-md'\n                        : 'border-gray-200 bg-white hover:border-blue-300 hover:bg-blue-25'\n                    }`}\n                  >\n                    <div className=\"flex items-center gap-4\">\n                      <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center font-bold ${\n                        isSelected\n                          ? 'border-blue-500 bg-blue-500 text-white'\n                          : 'border-gray-300 text-gray-600'\n                      }`}>\n                        {isSelected ? <TbCheck className=\"w-5 h-5\" /> : key}\n                      </div>\n                      <span className=\"text-gray-900 font-medium\">{value}</span>\n                    </div>\n                  </button>\n                );\n              })}\n\n              {/* Fill-in-the-Blank Questions */}\n              {safeQuestionData.type === 'fill' && (\n                <div className=\"space-y-4\">\n                  <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border-2 border-green-200\">\n                    <div className=\"flex items-center gap-2 mb-4\">\n                      <div className=\"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center\">\n                        <TbEdit className=\"w-5 h-5 text-white\" />\n                      </div>\n                      <label className=\"text-sm font-semibold text-gray-700\">\n                        Your Answer:\n                      </label>\n                    </div>\n                    <textarea\n                      value={currentAnswer}\n                      onChange={(e) => handleAnswerChange(e.target.value)}\n                      placeholder=\"Type your answer here...\"\n                      className=\"w-full p-4 border-2 border-green-300 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-200 outline-none resize-none transition-all\"\n                      rows=\"4\"\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* SIMPLE NAVIGATION */}\n      <div style={{background: 'white', padding: '20px'}}>\n        <button\n          onClick={onPrevious}\n          disabled={questionIndex === 0}\n          style={{padding: '10px 20px', marginRight: '10px', background: 'gray', color: 'white'}}\n        >\n          Previous\n        </button>\n        <button\n          onClick={onNext}\n          style={{padding: '10px 20px', background: 'blue', color: 'white'}}\n        >\n          {questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default NewQuizRenderer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SACEC,OAAO,EACPC,WAAW,EACXC,YAAY,EACZC,OAAO,EACPC,GAAG,EACHC,OAAO,EACPC,MAAM,EACNC,aAAa,EACbC,QAAQ,QACH,gBAAgB;AACvB,OAAO,uBAAuB;;AAE9B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,mBAAmB,GAAIC,QAAQ,IAAK;EACxC,IAAI,CAACA,QAAQ,EAAE,OAAO,IAAI;EAE1B,MAAMC,YAAY,GAAGD,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACA,QAAQ,IAAIA,QAAQ,CAACG,IAAI,IAAIH,QAAQ,CAACI,KAAK,IAAI,EAAE;EAChG,MAAMC,YAAY,GAAGL,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACO,UAAU,IAAIP,QAAQ,CAACK,YAAY,IAAI,KAAK;EAC3F,MAAMG,eAAe,GAAGR,QAAQ,CAACS,OAAO,IAAIT,QAAQ,CAACU,OAAO,IAAIV,QAAQ,CAACW,OAAO,IAAI,CAAC,CAAC;EACtF,MAAMC,aAAa,GAAGZ,QAAQ,CAACY,aAAa,IAAIZ,QAAQ,CAACa,aAAa,IAAI,EAAE;EAC5E,MAAMC,QAAQ,GAAGd,QAAQ,CAACc,QAAQ,IAAId,QAAQ,CAACe,KAAK,IAAI,EAAE;;EAE1D;EACA,IAAIC,cAAc,GAAG,KAAK;EAC1B,MAAMC,UAAU,GAAGC,MAAM,CAACb,YAAY,CAAC,CAACc,WAAW,CAAC,CAAC;EAErD,IAAIF,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,IAAIH,UAAU,KAAK,KAAK,IAAIA,UAAU,CAACG,QAAQ,CAAC,UAAU,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,EAAE;IAC7HJ,cAAc,GAAG,KAAK;EACxB,CAAC,MAAM,IAAIC,UAAU,CAACG,QAAQ,CAAC,MAAM,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,UAAU,KAAK,MAAM,IAAIA,UAAU,CAACG,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC9HJ,cAAc,GAAG,MAAM;EACzB,CAAC,MAAM,IAAIC,UAAU,CAACG,QAAQ,CAAC,OAAO,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,SAAS,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,SAAS,CAAC,EAAE;IAC3GJ,cAAc,GAAG,OAAO;EAC1B;;EAEA;EACA,IAAIA,cAAc,KAAK,KAAK,KAAK,CAACR,eAAe,IAAIa,MAAM,CAACC,IAAI,CAACd,eAAe,CAAC,CAACe,MAAM,KAAK,CAAC,CAAC,EAAE;IAC/FP,cAAc,GAAG,MAAM;EACzB;EAEA,OAAO;IACLb,IAAI,EAAEF,YAAY;IAClBK,IAAI,EAAEU,cAAc;IACpBP,OAAO,EAAED,eAAe;IACxBI,aAAa;IACbE,QAAQ;IACRU,gBAAgB,EAAExB;EACpB,CAAC;AACH,CAAC;;AAED;AACA,MAAMyB,UAAU,GAAIC,OAAO,IAAK;EAC9B,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAG,EAAE,CAAC;EACxC,MAAMI,gBAAgB,GAAGJ,OAAO,GAAG,EAAE;EACrC,OAAQ,GAAEC,OAAQ,IAAGG,gBAAgB,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAE,EAAC;AACrE,CAAC;AAED,MAAMC,eAAe,GAAGA,CAAC;EACvBjC,QAAQ;EACRkC,aAAa;EACbC,cAAc;EACdC,cAAc;EACdC,eAAe,GAAG,CAAC,CAAC;EACpBC,cAAc;EACdC,QAAQ;EACRC,MAAM;EACNC,UAAU;EACVC,SAAS,GAAG,MAAM;EAClBC,aAAa,GAAG,KAAK;EACrBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGhE,QAAQ,CAACqD,cAAc,IAAI,EAAE,CAAC;EACxE,MAAM,CAACY,UAAU,EAAEC,aAAa,CAAC,GAAGlE,QAAQ,CAAC,KAAK,CAAC;;EAEnD;EACA,MAAMmE,YAAY,GAAG,IAAI,CAAC,CAAC;;EAE3B;EACAlE,SAAS,CAAC,MAAM;IACdmE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;MACvClB,aAAa;MACbC,cAAc;MACdkB,WAAW,EAAE,CAAC,CAACrD,QAAQ;MACvBsD,eAAe,EAAE,CAAC,CAACJ,YAAY;MAC/B7C,YAAY,EAAE,CAAAL,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,IAAI,MAAIN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,UAAU;MACpDN,YAAY,EAAE,CAAAD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,IAAI,MAAIF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEA,QAAQ;MAClDuC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvC,QAAQ,EAAEkD,YAAY,EAAEhB,aAAa,EAAEC,cAAc,EAAEI,QAAQ,CAAC,CAAC;;EAErE;EACAvD,SAAS,CAAC,MAAM;IACd+D,gBAAgB,CAACX,cAAc,IAAI,EAAE,CAAC;IACtCa,aAAa,CAAC,CAAC,CAACb,cAAc,CAAC;EACjC,CAAC,EAAE,CAACA,cAAc,EAAEF,aAAa,CAAC,CAAC;;EAEnC;EACA,MAAMqB,kBAAkB,GAAGtE,WAAW,CAAEuE,MAAM,IAAK;IACjDT,gBAAgB,CAACS,MAAM,CAAC;IACxBP,aAAa,CAAC,CAAC,CAACO,MAAM,CAAC;IACvBlB,cAAc,CAACkB,MAAM,CAAC;EACxB,CAAC,EAAE,CAAClB,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMmB,UAAU,GAAGxE,WAAW,CAAC,MAAM;IACnC,IAAIiD,aAAa,KAAKC,cAAc,GAAG,CAAC,EAAE;MACxC;MACA,IAAIS,QAAQ,EAAE;QACZA,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC,MAAM;MACLJ,MAAM,CAAC,CAAC;IACV;EACF,CAAC,EAAE,CAACN,aAAa,EAAEC,cAAc,EAAEK,MAAM,EAAEI,QAAQ,CAAC,CAAC;EAErD,MAAMc,cAAc,GAAGzE,WAAW,CAAC,MAAM;IACvC,IAAIiD,aAAa,GAAG,CAAC,EAAE;MACrBO,UAAU,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACP,aAAa,EAAEO,UAAU,CAAC,CAAC;;EAE/B;EACA;EACA,MAAMkB,gBAAgB,GAAG;IACvBxD,IAAI,EAAE,CAAAH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEE,IAAI,MAAIF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEA,QAAQ,MAAIA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEG,IAAI,KAAI,+BAA+B;IAC/FG,IAAI,EAAE,CAAAN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEM,IAAI,MAAIN,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEO,UAAU,KAAI,KAAK;IACrDE,OAAO,EAAE,CAAAT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAES,OAAO,MAAIT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEU,OAAO,KAAI;MACjDkD,CAAC,EAAE,CAAA5D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE6D,OAAO,KAAI,aAAa;MACrCC,CAAC,EAAE,CAAA9D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE+D,OAAO,KAAI,aAAa;MACrCC,CAAC,EAAE,CAAAhE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEiE,OAAO,KAAI,aAAa;MACrCC,CAAC,EAAE,CAAAlE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEmE,OAAO,KAAI;IAC1B,CAAC;IACDrD,QAAQ,EAAE,CAAAd,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEc,QAAQ,MAAId,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEe,KAAK,MAAIf,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEoE,OAAO;EACtE,CAAC;;EAED;EACAjB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;IAC3CC,WAAW,EAAE,CAAC,CAACrD,QAAQ;IACvBsD,eAAe,EAAE,CAAC,CAACJ,YAAY;IAC/BS,gBAAgB;IAChBzB,aAAa;IACbC,cAAc;IACdkC,WAAW,EAAErE;EACf,CAAC,CAAC;EAEF,oBACEF,OAAA;IAAKwE,SAAS,EAAC,2DAA2D;IAAAC,QAAA,gBAExEzE,OAAA;MAAKwE,SAAS,EAAC,iEAAiE;MAAAC,QAAA,eAC9EzE,OAAA;QAAKwE,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CzE,OAAA;UAAKwE,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAEhDzE,OAAA;YAAKwE,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBzE,OAAA;cAAIwE,SAAS,EAAC,0CAA0C;cAAAC,QAAA,EACrD7B;YAAS;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACL7E,OAAA;cAAGwE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,WAC/B,EAACrC,aAAa,GAAG,CAAC,EAAC,MAAI,EAACC,cAAc;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGN7E,OAAA;YAAKwE,SAAS,EAAG,qFACf3B,aAAa,GACT,uCAAuC,GACvC,2BACL,EAAE;YAAA4B,QAAA,gBACDzE,OAAA,CAACV,OAAO;cAACkF,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/B7E,OAAA;cAAAyE,QAAA,EAAO9C,UAAU,CAACc,QAAQ;YAAC;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7E,OAAA;UAAKwE,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACnBzE,OAAA;YAAKwE,SAAS,EAAC,qCAAqC;YAAAC,QAAA,eAClDzE,OAAA;cACEwE,SAAS,EAAC,2FAA2F;cACrGM,KAAK,EAAE;gBAAEC,KAAK,EAAG,GAAG,CAAC3C,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAI;cAAG;YAAE;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN7E,OAAA;YAAKwE,SAAS,EAAC,iDAAiD;YAAAC,QAAA,gBAC9DzE,OAAA;cAAAyE,QAAA,EAAM;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrB7E,OAAA;cAAAyE,QAAA,GAAO3C,IAAI,CAACkD,KAAK,CAAE,CAAC5C,aAAa,GAAG,CAAC,IAAIC,cAAc,GAAI,GAAG,CAAC,EAAC,GAAC;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7E,OAAA;MAAKwE,SAAS,EAAC,6BAA6B;MAAAC,QAAA,eAC1CzE,OAAA;QAAKwE,SAAS,EAAC,uEAAuE;QAAAC,QAAA,gBAEpFzE,OAAA;UAAKwE,SAAS,EAAC,wDAAwD;UAAAC,QAAA,eACrEzE,OAAA;YAAKwE,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDzE,OAAA;cAAKwE,SAAS,EAAG,gDACfX,gBAAgB,CAACrD,IAAI,KAAK,KAAK,GAC3B,2BAA2B,GAC3BqD,gBAAgB,CAACrD,IAAI,KAAK,MAAM,GAChC,6BAA6B,GAC7B,+BACL,EAAE;cAAAiE,QAAA,EACAZ,gBAAgB,CAACrD,IAAI,KAAK,KAAK,GAAG,iBAAiB,GACnDqD,gBAAgB,CAACrD,IAAI,KAAK,MAAM,GAAG,mBAAmB,GACtD;YAAgB;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,EACL7B,aAAa,iBACZhD,OAAA;cAAKwE,SAAS,EAAC,wDAAwD;cAAAC,QAAA,gBACrEzE,OAAA,CAACP,OAAO;gBAAC+E,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,YAEjC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7E,OAAA;UAAKwE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,gBAE/CzE,OAAA;YAAKwE,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnBzE,OAAA;cAAIwE,SAAS,EAAC,0DAA0D;cAAAC,QAAA,EACrEZ,gBAAgB,CAACxD;YAAI;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,EAGJhB,gBAAgB,CAAC7C,QAAQ,iBACxBhB,OAAA;cAAKwE,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBzE,OAAA;gBAAKwE,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,gBACpFzE,OAAA;kBACEiF,GAAG,EAAEpB,gBAAgB,CAAC7C,QAAS;kBAC/BkE,GAAG,EAAC,kBAAkB;kBACtBV,SAAS,EAAC,yDAAyD;kBACnEM,KAAK,EAAE;oBAAEK,OAAO,EAAE;kBAAQ,CAAE;kBAC5BC,OAAO,EAAGC,CAAC,IAAK;oBACdA,CAAC,CAACC,MAAM,CAACR,KAAK,CAACK,OAAO,GAAG,MAAM;oBAC/BE,CAAC,CAACC,MAAM,CAACC,WAAW,CAACT,KAAK,CAACK,OAAO,GAAG,MAAM;kBAC7C;gBAAE;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACF7E,OAAA;kBAAKwE,SAAS,EAAC,uDAAuD;kBAAAC,QAAA,eACpEzE,OAAA;oBAAKwE,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BzE,OAAA,CAACL,OAAO;sBAAC6E,SAAS,EAAC;oBAAwB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9C7E,OAAA;sBAAAyE,QAAA,EAAG;oBAAmB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN7E,OAAA;YAAKwE,SAAS,EAAC,WAAW;YAAAC,QAAA,GAEvBZ,gBAAgB,CAACrD,IAAI,KAAK,KAAK,IAAIqD,gBAAgB,CAAClD,OAAO,IAAIY,MAAM,CAACiE,OAAO,CAAC3B,gBAAgB,CAAClD,OAAO,CAAC,CAAC8E,GAAG,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;cAC7H,MAAMC,UAAU,GAAG5C,aAAa,KAAK0C,GAAG;cACxC,oBACE1F,OAAA;gBAEE6F,OAAO,EAAEA,CAAA,KAAMpC,kBAAkB,CAACiC,GAAG,CAAE;gBACvClB,SAAS,EAAG,wEACVoB,UAAU,GACN,sCAAsC,GACtC,iEACL,EAAE;gBAAAnB,QAAA,eAEHzE,OAAA;kBAAKwE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtCzE,OAAA;oBAAKwE,SAAS,EAAG,4EACfoB,UAAU,GACN,wCAAwC,GACxC,+BACL,EAAE;oBAAAnB,QAAA,EACAmB,UAAU,gBAAG5F,OAAA,CAACP,OAAO;sBAAC+E,SAAS,EAAC;oBAAS;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,GAAGa;kBAAG;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC,eACN7E,OAAA;oBAAMwE,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAEkB;kBAAK;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC,GAjBDa,GAAG;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAkBF,CAAC;YAEb,CAAC,CAAC,EAGDhB,gBAAgB,CAACrD,IAAI,KAAK,MAAM,iBAC/BR,OAAA;cAAKwE,SAAS,EAAC,WAAW;cAAAC,QAAA,eACxBzE,OAAA;gBAAKwE,SAAS,EAAC,uFAAuF;gBAAAC,QAAA,gBACpGzE,OAAA;kBAAKwE,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAC3CzE,OAAA;oBAAKwE,SAAS,EAAC,oEAAoE;oBAAAC,QAAA,eACjFzE,OAAA,CAACJ,MAAM;sBAAC4E,SAAS,EAAC;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtC,CAAC,eACN7E,OAAA;oBAAOwE,SAAS,EAAC,qCAAqC;oBAAAC,QAAA,EAAC;kBAEvD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACN7E,OAAA;kBACE2F,KAAK,EAAE3C,aAAc;kBACrB8C,QAAQ,EAAGT,CAAC,IAAK5B,kBAAkB,CAAC4B,CAAC,CAACC,MAAM,CAACK,KAAK,CAAE;kBACpDI,WAAW,EAAC,0BAA0B;kBACtCvB,SAAS,EAAC,kJAAkJ;kBAC5JwB,IAAI,EAAC;gBAAG;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7E,OAAA;MAAK8E,KAAK,EAAE;QAACmB,UAAU,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAM,CAAE;MAAAzB,QAAA,gBACjDzE,OAAA;QACE6F,OAAO,EAAElD,UAAW;QACpBwD,QAAQ,EAAE/D,aAAa,KAAK,CAAE;QAC9B0C,KAAK,EAAE;UAACoB,OAAO,EAAE,WAAW;UAAEE,WAAW,EAAE,MAAM;UAAEH,UAAU,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAO,CAAE;QAAA5B,QAAA,EACxF;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT7E,OAAA;QACE6F,OAAO,EAAEnD,MAAO;QAChBoC,KAAK,EAAE;UAACoB,OAAO,EAAE,WAAW;UAAED,UAAU,EAAE,MAAM;UAAEI,KAAK,EAAE;QAAO,CAAE;QAAA5B,QAAA,EAEjErC,aAAa,KAAKC,cAAc,GAAG,CAAC,GAAG,QAAQ,GAAG;MAAM;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9B,EAAA,CAxQIZ,eAAe;AAAAmE,EAAA,GAAfnE,eAAe;AA0QrB,eAAeA,eAAe;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}